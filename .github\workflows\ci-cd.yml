name: HarmoniHSE360 CI/CD (Legacy)

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  DOTNET_VERSION: '8.0.x'
  NODE_VERSION: '20.x'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: harmonihse360_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}
        cache: true

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: src/HarmoniHSE360.Web/ClientApp/package-lock.json

    - name: Restore .NET dependencies
      run: dotnet restore

    - name: Install Node.js dependencies
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm ci

    - name: Build .NET application
      run: dotnet build --no-restore --configuration Release

    - name: Build React application
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm run build

    - name: Run .NET tests
      run: dotnet test --no-build --verbosity normal --configuration Release --collect:"XPlat Code Coverage"
      env:
        ConnectionStrings__DefaultConnection: "Host=localhost;Port=5432;Database=harmonihse360_test;Username=test;Password=test"
        ConnectionStrings__Redis: "localhost:6379"
        Jwt__Key: "TestJwtKeyThatIsAtLeast32CharactersLongForTesting"

    - name: Run Frontend tests
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm test -- --coverage --watchAll=false

    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      continue-on-error: true
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}

  code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: src/HarmoniHSE360.Web/ClientApp/package-lock.json

    - name: Restore .NET dependencies
      run: dotnet restore

    - name: Install Node.js dependencies
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm ci

    - name: Check .NET formatting
      run: dotnet format --verify-no-changes --verbosity diagnostic

    - name: Run ESLint
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm run lint

    - name: Run Prettier check
      working-directory: src/HarmoniHSE360.Web/ClientApp
      run: npm run format:check

  docker-build:
    needs: [test, code-quality]
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    permissions:
      contents: read
      packages: write

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to the Container registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.flyio
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64

  deploy-staging:
    needs: docker-build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Deploy to Fly.io Staging
      run: flyctl deploy --config fly.staging.toml
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN_STAGING }}

    - name: Health check
      run: |
        sleep 30
        curl -f https://harmonihse360-staging.fly.dev/health || exit 1

  deploy-production:
    needs: docker-build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Deploy to Fly.io Production
      run: flyctl deploy --config fly.toml
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Health check
      run: |
        sleep 30
        curl -f https://harmonihse360-app.fly.dev/health || exit 1