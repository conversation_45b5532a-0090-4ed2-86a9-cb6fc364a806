# HarmoniHSE360 Environment Configuration
# Copy this file to .env and update with your values

# Application Settings
APP_PORT=8080
NGINX_PORT=80
NGINX_HTTPS_PORT=443

# Database Configuration
POSTGRES_DB=HarmoniHSE360
POSTGRES_USER=postgres
POSTGRES_PASSWORD=YourStrongProductionPassword123!

# Redis Configuration
REDIS_PASSWORD=YourRedisProductionPassword123!

# JWT Configuration
JWT_KEY=YourSuperSecretProductionJwtKeyThatMustBeAtLeast32CharactersLong!
JWT_ISSUER=HarmoniHSE360
JWT_AUDIENCE=HarmoniHSE360Users
JWT_EXPIRATION_MINUTES=60
JWT_REFRESH_EXPIRATION_DAYS=7

# Email Configuration (optional)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=YourEmailPassword
EMAIL_FROM=HarmoniHSE360 <<EMAIL>>

# File Storage Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_EXTENSIONS=.jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx

# Logging
LOG_LEVEL=Information
LOG_PATH=/app/logs

# CORS Settings (for production, specify your domain)
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com