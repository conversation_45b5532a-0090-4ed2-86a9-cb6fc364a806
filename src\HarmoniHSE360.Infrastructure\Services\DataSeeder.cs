using HarmoniHSE360.Application.Common.Interfaces;
using HarmoniHSE360.Domain.Entities;
using HarmoniHSE360.Domain.ValueObjects;
using HarmoniHSE360.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace HarmoniHSE360.Infrastructure.Services;

public interface IDataSeeder
{
    Task SeedAsync();
}

public class DataSeeder : IDataSeeder
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<DataSeeder> _logger;
    private readonly IConfiguration _configuration;

    public DataSeeder(ApplicationDbContext context, ILogger<DataSeeder> logger, IConfiguration configuration)
    {
        _context = context;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task SeedAsync()
    {
        try
        {
            await SeedRolesAndPermissionsAsync();
            await _context.SaveChangesAsync(); // Save roles first

            await SeedUsersAsync();
            await _context.SaveChangesAsync(); // Save users

            // Seed incidents if enabled in configuration
            var seedIncidents = _configuration["DataSeeding:SeedIncidents"] != "false";
            if (seedIncidents)
            {
                await SeedIncidentsAsync();
                await _context.SaveChangesAsync(); // Save incidents
            }

            _logger.LogInformation("Database seeding completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred while seeding database");
            throw;
        }
    }

    private async Task SeedRolesAndPermissionsAsync()
    {
        var roleCount = await _context.Roles.CountAsync();
        _logger.LogInformation("Current role count: {RoleCount}", roleCount);

        if (roleCount > 0)
        {
            _logger.LogInformation("Roles already exist, skipping seeding");
            return;
        }

        _logger.LogInformation("Starting role and permission seeding...");

        var adminRole = Role.Create("Admin", "System Administrator");
        var hseManagerRole = Role.Create("HSEManager", "HSE Manager");
        var employeeRole = Role.Create("Employee", "Regular Employee");

        await _context.Roles.AddRangeAsync(adminRole, hseManagerRole, employeeRole);
        await _context.SaveChangesAsync(); // Save roles first to get IDs

        // Create unique permissions first
        var allPermissions = new List<Permission>
        {
            Permission.Create("incidents.view", "View Incidents", "Can view incident reports", "Incidents"),
            Permission.Create("incidents.create", "Create Incidents", "Can create new incident reports", "Incidents"),
            Permission.Create("incidents.update", "Update Incidents", "Can modify incident reports", "Incidents"),
            Permission.Create("incidents.delete", "Delete Incidents", "Can delete incident reports", "Incidents"),
            Permission.Create("incidents.investigate", "Investigate Incidents", "Can investigate and assign incidents", "Incidents"),
            Permission.Create("users.manage", "Manage Users", "Can manage user accounts and roles", "Users"),
            Permission.Create("reports.view", "View Reports", "Can view all reports and analytics", "Reports"),
            Permission.Create("reports.create", "Create Reports", "Can generate reports", "Reports")
        };

        await _context.Permissions.AddRangeAsync(allPermissions);
        await _context.SaveChangesAsync(); // Save permissions first

        // Get permission references for role assignment
        var incidentsView = allPermissions.First(p => p.Name == "incidents.view");
        var incidentsCreate = allPermissions.First(p => p.Name == "incidents.create");
        var incidentsUpdate = allPermissions.First(p => p.Name == "incidents.update");
        var incidentsDelete = allPermissions.First(p => p.Name == "incidents.delete");
        var incidentsInvestigate = allPermissions.First(p => p.Name == "incidents.investigate");
        var usersManage = allPermissions.First(p => p.Name == "users.manage");
        var reportsView = allPermissions.First(p => p.Name == "reports.view");
        var reportsCreate = allPermissions.First(p => p.Name == "reports.create");

        // Assign permissions to roles
        // Admin gets all permissions
        adminRole.AddPermission(incidentsView);
        adminRole.AddPermission(incidentsCreate);
        adminRole.AddPermission(incidentsUpdate);
        adminRole.AddPermission(incidentsDelete);
        adminRole.AddPermission(incidentsInvestigate);
        adminRole.AddPermission(usersManage);
        adminRole.AddPermission(reportsView);
        adminRole.AddPermission(reportsCreate);

        // HSE Manager gets incident and report permissions
        hseManagerRole.AddPermission(incidentsView);
        hseManagerRole.AddPermission(incidentsCreate);
        hseManagerRole.AddPermission(incidentsUpdate);
        hseManagerRole.AddPermission(incidentsInvestigate);
        hseManagerRole.AddPermission(reportsView);
        hseManagerRole.AddPermission(reportsCreate);

        // Employee gets basic incident permissions
        employeeRole.AddPermission(incidentsView);
        employeeRole.AddPermission(incidentsCreate);
    }

    private async Task SeedUsersAsync()
    {
        if (await _context.Users.AnyAsync())
            return;

        var adminRole = await _context.Roles.FirstAsync(r => r.Name == "Admin");
        var hseManagerRole = await _context.Roles.FirstAsync(r => r.Name == "HSEManager");
        var employeeRole = await _context.Roles.FirstAsync(r => r.Name == "Employee");

        // Hash demo passwords for production use
        var passwordHashService = new PasswordHashService();

        var users = new List<User>
        {
            User.Create("<EMAIL>", passwordHashService.HashPassword("Admin123!"), "System Administrator", "ADM001", "IT", "System Administrator"),
            User.Create("<EMAIL>", passwordHashService.HashPassword("HSE123!"), "HSE Manager", "HSE001", "Health & Safety", "HSE Manager"),
            User.Create("<EMAIL>", passwordHashService.HashPassword("Employee123!"), "John Doe", "EMP001", "Facilities", "Maintenance Supervisor"),
            User.Create("<EMAIL>", passwordHashService.HashPassword("Employee123!"), "Jane Smith", "EMP002", "Academic", "Teacher")
        };

        // Assign roles
        users[0].AssignRole(adminRole);
        users[1].AssignRole(hseManagerRole);
        users[2].AssignRole(employeeRole);
        users[3].AssignRole(employeeRole);

        await _context.Users.AddRangeAsync(users);
    }

    private async Task SeedIncidentsAsync()
    {
        // Check if we should re-seed incidents even if they exist
        var reSeedIncidents = _configuration["DataSeeding:ReSeedIncidents"] == "true";
        
        if (!reSeedIncidents && await _context.Incidents.AnyAsync())
        {
            _logger.LogInformation("Incidents already exist and ReSeedIncidents is false, skipping incident seeding");
            return;
        }

        _logger.LogInformation("Starting incident seeding...");

        // If re-seeding is enabled, clear existing incidents first
        if (reSeedIncidents && await _context.Incidents.AnyAsync())
        {
            _logger.LogInformation("Clearing existing incidents for re-seeding...");
            _context.Incidents.RemoveRange(_context.Incidents);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Existing incidents cleared");
        }

        // Get users for incident reporting
        var hseManager = await _context.Users.FirstAsync(u => u.Email == "<EMAIL>");
        var employee1 = await _context.Users.FirstAsync(u => u.Email == "<EMAIL>");
        var employee2 = await _context.Users.FirstAsync(u => u.Email == "<EMAIL>");

        var incidents = new List<Incident>
        {
            // Critical incident - Fire alarm malfunction
            Incident.Create(
                "Fire alarm system malfunction in East Wing",
                "False alarm triggered at 11:45 AM causing evacuation of entire East Wing building. System showed smoke detection in Room 304 but no smoke was present. Maintenance team investigated and found faulty sensor.",
                IncidentSeverity.Serious,
                DateTime.UtcNow.AddDays(-5).AddHours(11).AddMinutes(45),
                "East Wing - 3rd Floor, Room 304",
                "Emily Chen",
                "<EMAIL>",
                "Facilities Management",
                GeoLocation.Create(-6.2088, 106.8456) // BSJ coordinates
            ),

            // Moderate incident - Chemistry lab accident
            Incident.Create(
                "Student minor burn injury in Chemistry Lab",
                "Grade 11 student suffered minor burn on left hand during chemistry practical. Student was heating solution and accidentally touched hot beaker. First aid administered immediately.",
                IncidentSeverity.Moderate,
                DateTime.UtcNow.AddDays(-3).AddHours(14).AddMinutes(30),
                "Science Building - Chemistry Lab Room 205",
                "Dr. Sarah Johnson",
                "<EMAIL>",
                "Science Department",
                GeoLocation.Create(-6.2090, 106.8458)
            ),

            // Minor incident - Slip and fall
            Incident.Create(
                "Slip and fall incident near main entrance",
                "Staff member slipped on wet floor near main entrance during rainy weather. Warning sign was not properly placed. No serious injury, but staff member experienced minor bruising.",
                IncidentSeverity.Minor,
                DateTime.UtcNow.AddDays(-7).AddHours(9).AddMinutes(15),
                "Main Building - Ground Floor Entrance",
                "David Wilson",
                "<EMAIL>",
                "Administration",
                GeoLocation.Create(-6.2085, 106.8455)
            ),

            // Critical incident - Playground equipment failure
            Incident.Create(
                "Playground equipment structural failure",
                "Swing set chain broke while student was using it. Student fell but landed safely on rubber matting. Equipment immediately cordoned off. Inspection revealed metal fatigue in chain links.",
                IncidentSeverity.Critical,
                DateTime.UtcNow.AddDays(-2).AddHours(10).AddMinutes(30),
                "Primary School Playground - Area B",
                "Lisa Martinez",
                "<EMAIL>",
                "Primary School",
                GeoLocation.Create(-6.2092, 106.8460)
            ),

            // Moderate incident - Food poisoning
            Incident.Create(
                "Multiple students report food poisoning symptoms",
                "12 students from Grade 9 reported nausea and stomach pain after lunch. All had consumed chicken sandwich from cafeteria. Health office provided treatment, parents notified.",
                IncidentSeverity.Serious,
                DateTime.UtcNow.AddDays(-10).AddHours(13).AddMinutes(45),
                "School Cafeteria",
                "Nurse Patricia",
                "<EMAIL>",
                "Health Services",
                GeoLocation.Create(-6.2087, 106.8457)
            ),

            // Minor incident - Sports injury
            Incident.Create(
                "Student ankle sprain during PE class",
                "Grade 8 student twisted ankle during basketball game in PE class. Ice pack applied, student sent to health office. Parents contacted for pickup.",
                IncidentSeverity.Minor,
                DateTime.UtcNow.AddDays(-4).AddHours(15).AddMinutes(20),
                "Sports Hall - Basketball Court 2",
                "Coach Michael Brown",
                "<EMAIL>",
                "Physical Education",
                GeoLocation.Create(-6.2089, 106.8459)
            )
        };

        // Update incident statuses and add additional details
        incidents[0].UpdateStatus(IncidentStatus.AwaitingAction);
        incidents[0].UpdateInjuryDetails(InjuryType.None, false, false);
        incidents[0].AddWitnessInformation("Multiple staff members from East Wing");
        incidents[0].RecordImmediateActions("Building evacuated, Fire department notified, Maintenance team dispatched");

        incidents[1].UpdateStatus(IncidentStatus.UnderInvestigation);
        incidents[1].UpdateInjuryDetails(InjuryType.Burn, true, false);
        incidents[1].AddWitnessInformation("Lab assistant James Wong, 3 other students");
        incidents[1].RecordImmediateActions("First aid administered, Cold water applied to burn, Parents notified");

        incidents[2].UpdateStatus(IncidentStatus.Resolved);
        incidents[2].UpdateInjuryDetails(InjuryType.Bruise, false, false);
        incidents[2].AddWitnessInformation("Security guard on duty");
        incidents[2].RecordImmediateActions("Area dried and warning signs placed, Incident report filed");

        incidents[3].UpdateStatus(IncidentStatus.AwaitingAction);
        incidents[3].UpdateInjuryDetails(InjuryType.None, true, false);
        incidents[3].AddWitnessInformation("PE Teacher, 5 students");
        incidents[3].RecordImmediateActions("Equipment cordoned off, All playground equipment scheduled for inspection");

        incidents[4].UpdateStatus(IncidentStatus.Closed);
        incidents[4].UpdateInjuryDetails(InjuryType.Other, true, false);
        incidents[4].AddWitnessInformation("Cafeteria staff");
        incidents[4].RecordImmediateActions("Food samples collected, Kitchen inspection conducted, Menu item removed");

        incidents[5].UpdateStatus(IncidentStatus.Resolved);
        incidents[5].UpdateInjuryDetails(InjuryType.Sprain, true, false);
        incidents[5].AddWitnessInformation("Other students in PE class");
        incidents[5].RecordImmediateActions("RICE protocol applied, Student sent to health office");

        // Add some corrective actions to resolved/closed incidents
        var correctiveAction1 = CorrectiveAction.Create(
            "Replace all faulty smoke sensors in East Wing",
            "Maintenance",
            DateTime.UtcNow.AddDays(5),
            ActionPriority.High
        );
        incidents[0].AddCorrectiveAction(correctiveAction1);

        var correctiveAction2 = CorrectiveAction.Create(
            "Review and update chemistry lab safety protocols",
            "Science Department",
            DateTime.UtcNow.AddDays(7),
            ActionPriority.Medium
        );
        incidents[1].AddCorrectiveAction(correctiveAction2);

        var correctiveAction3 = CorrectiveAction.Create(
            "Install additional wet floor warning signs at all entrances",
            "Facilities",
            DateTime.UtcNow.AddDays(3),
            ActionPriority.Medium
        );
        correctiveAction3.MarkAsCompleted(DateTime.UtcNow.AddDays(-1), "20 new warning signs installed");
        incidents[2].AddCorrectiveAction(correctiveAction3);

        await _context.Incidents.AddRangeAsync(incidents);
        _logger.LogInformation("Seeded {Count} incidents", incidents.Count);
    }
}