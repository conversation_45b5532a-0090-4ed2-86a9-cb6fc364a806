using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using HarmoniHSE360.Application.Common.Interfaces;
using HarmoniHSE360.Application.Features.Incidents.DTOs;

namespace HarmoniHSE360.Application.Features.Incidents.Commands;

public class UpdateIncidentCommandHandler : IRequestHandler<UpdateIncidentCommand, IncidentDto?>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<UpdateIncidentCommandHandler> _logger;

    public UpdateIncidentCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<UpdateIncidentCommandHandler> logger)
    {
        _context = context;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    public async Task<IncidentDto?> Handle(UpdateIncidentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating incident {IncidentId} by user {UserEmail}", 
            request.Id, _currentUserService.Email);

        var incident = await _context.Incidents
            .Include(i => i.Attachments)
            .Include(i => i.InvolvedPersons)
            .Include(i => i.CorrectiveActions)
            .FirstOrDefaultAsync(i => i.Id == request.Id, cancellationToken);

        if (incident == null)
        {
            _logger.LogWarning("Incident {IncidentId} not found", request.Id);
            return null;
        }

        // Update basic details
        incident.UpdateDetails(request.Title, request.Description, _currentUserService.Email!);
        
        // Update status if changed
        if (incident.Status != request.Status)
        {
            incident.UpdateStatus(request.Status);
        }

        // Update location
        if (request.Latitude.HasValue && request.Longitude.HasValue)
        {
            incident.SetGeoLocation(request.Latitude.Value, request.Longitude.Value);
        }

        await _context.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Incident {IncidentId} updated successfully", incident.Id);

        // Return updated DTO
        return new IncidentDto
        {
            Id = incident.Id,
            Title = incident.Title,
            Description = incident.Description,
            Severity = incident.Severity.ToString(),
            Status = incident.Status.ToString(),
            IncidentDate = incident.IncidentDate,
            Location = incident.Location,
            Latitude = incident.GeoLocation?.Latitude,
            Longitude = incident.GeoLocation?.Longitude,
            ReporterName = incident.ReporterName,
            ReporterEmail = incident.ReporterEmail,
            ReporterDepartment = incident.ReporterDepartment,
            InjuryType = incident.InjuryType?.ToString(),
            MedicalTreatmentProvided = incident.MedicalTreatmentProvided,
            EmergencyServicesContacted = incident.EmergencyServicesContacted,
            WitnessNames = incident.WitnessNames,
            ImmediateActionsTaken = incident.ImmediateActionsTaken,
            AttachmentsCount = incident.Attachments.Count,
            InvolvedPersonsCount = incident.InvolvedPersons.Count,
            CorrectiveActionsCount = incident.CorrectiveActions.Count,
            CreatedAt = incident.CreatedAt,
            CreatedBy = incident.CreatedBy,
            LastModifiedAt = incident.LastModifiedAt,
            LastModifiedBy = incident.LastModifiedBy
        };
    }
}