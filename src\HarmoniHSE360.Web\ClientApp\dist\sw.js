if(!self.define){let e,s={};const i=(i,n)=>(i=new URL(i+".js",n).href,s[i]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()})).then((()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e})));self.define=(n,r)=>{const l=e||("document"in self?document.currentScript.src:"")||location.href;if(s[l])return;let t={};const o=e=>i(e,l),u={module:{uri:l},exports:t,require:o};s[l]=Promise.all(n.map((e=>u[e]||o(e)))).then((e=>(r(...e),t)))}}define(["./workbox-5ffe50d4"],(function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"assets/coreui-vendor-CeiuoDvP.js",revision:null},{url:"assets/CreateIncident-7ZWZLZ-w.js",revision:null},{url:"assets/Dashboard-CtaojPyc.js",revision:null},{url:"assets/IncidentList-BeYcGaGD.js",revision:null},{url:"assets/index-BbPvz14S.css",revision:null},{url:"assets/index-BDmn1jd7.js",revision:null},{url:"assets/index.esm-irDu7M73.js",revision:null},{url:"assets/Login-D-vNQc28.js",revision:null},{url:"assets/react-vendor-Dc0cLFd6.js",revision:null},{url:"assets/redux-vendor-Wiuug2_S.js",revision:null},{url:"index.html",revision:"661f72342befa0528f2f28d17a53f476"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"manifest.webmanifest",revision:"06487fe369e8288d24cd488038d603f4"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html")))}));
