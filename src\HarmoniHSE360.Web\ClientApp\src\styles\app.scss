// Use Sass modules instead of @import
@use 'sass:color';

// Harmoni HSE 360 Branding Colors
$teal-primary: #0097A7;
$deep-blue: #004D6E;
$leaf-green: #66BB6A;
$accent-yellow: #F9A825;
$soft-grey: #F5F5F5;
$charcoal: #212121;

// Import CoreUI with configuration
@use '@coreui/coreui/scss/coreui' with (
  $primary: $teal-primary,
  $secondary: $deep-blue,
  $success: $leaf-green,
  $warning: $accent-yellow,
  $body-bg: $soft-grey,
  $body-color: $charcoal
);

// Custom styles following Harmoni branding
:root {
  --harmoni-teal: #{$teal-primary};
  --harmoni-blue: #{$deep-blue};
  --harmoni-green: #{$leaf-green};
  --harmoni-yellow: #{$accent-yellow};
  --harmoni-grey: #{$soft-grey};
  --harmoni-charcoal: #{$charcoal};
}

// Typography
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 600;
}

// Buttons - Harmoni style
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;

  &.btn-primary {
    background-color: var(--harmoni-teal);
    border-color: var(--harmoni-teal);

    &:hover, &:focus {
      background-color: color.adjust($teal-primary, $lightness: -10%);
      border-color: color.adjust($teal-primary, $lightness: -10%);
    }
  }

  &.btn-secondary {
    background-color: transparent;
    color: var(--harmoni-teal);
    border: 1px solid var(--harmoni-teal);

    &:hover, &:focus {
      background-color: var(--harmoni-teal);
      color: white;
    }
  }

  &.btn-danger {
    background-color: var(--harmoni-yellow);
    border-color: var(--harmoni-yellow);
    color: var(--harmoni-charcoal);
  }
}

// Cards - Harmoni style
.card {
  background: #FFFFFF;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  border: none;
  border-radius: 8px;
  padding: 16px;

  .card-header {
    background-color: transparent;
    border-bottom: 2px solid var(--harmoni-teal);
    font-weight: 600;
  }
}

// Forms - Harmoni style
.form-control, .form-select {
  border: 1px solid #CCCCCC;
  border-radius: 8px;

  &:focus {
    border-color: var(--harmoni-teal);
    box-shadow: 0 0 0 0.2rem rgba(0, 151, 167, 0.25);
  }
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

// Sidebar customization
.sidebar {
  background-color: var(--harmoni-blue);

  .sidebar-brand {
    background-color: color.adjust($deep-blue, $lightness: -5%);
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
  }

  .nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;

    &:hover {
      color: white;
      background-color: rgba(255, 255, 255, 0.1);
      text-decoration: none;
    }

    &.active {
      color: white;
      background-color: var(--harmoni-teal);
      font-weight: 500;
    }
  }

  .nav-icon {
    color: var(--harmoni-teal);
    margin-right: 0.5rem;
    flex-shrink: 0;
  }

  // CoreUI nav item styling integration
  .nav-item {
    .nav-link {
      // Override CoreUI defaults for our custom navigation
      color: rgba(255, 255, 255, 0.8) !important;
    }
  }

  // Group navigation styling
  .nav-group {
    .nav-group-items {
      .nav-item {
        .nav-link {
          padding-left: 2.5rem;
          font-size: 0.875rem;
        }
      }
    }
  }
}

// Header customization
.header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

// Dashboard widgets
.dashboard-widget {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }

  .widget-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    &.primary {
      background-color: rgba(0, 151, 167, 0.1);
      color: var(--harmoni-teal);
    }

    &.success {
      background-color: rgba(102, 187, 106, 0.1);
      color: var(--harmoni-green);
    }

    &.warning {
      background-color: rgba(249, 168, 37, 0.1);
      color: var(--harmoni-yellow);
    }
  }
}

// Tables
.table {
  th {
    background-color: var(--harmoni-grey);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
  }

  tbody tr:hover {
    background-color: rgba(0, 151, 167, 0.05);
  }
}

// Alerts
.alert {
  border-radius: 8px;
  border: none;

  &.alert-info {
    background-color: rgba(0, 151, 167, 0.1);
    color: color.adjust($teal-primary, $lightness: -20%);
  }

  &.alert-success {
    background-color: rgba(102, 187, 106, 0.1);
    color: color.adjust($leaf-green, $lightness: -20%);
  }

  &.alert-warning {
    background-color: rgba(249, 168, 37, 0.1);
    color: color.adjust($accent-yellow, $lightness: -20%);
  }
}

// Loading states
.loading-spinner {
  color: var(--harmoni-teal);
}

// Mobile responsiveness
@media (max-width: 768px) {
  .card {
    padding: 12px;
  }

  .btn {
    min-height: 44px; // Touch target size
  }

  .dashboard-widget {
    padding: 16px;
  }
}

// Accessibility
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Focus states
*:focus {
  outline: 2px solid var(--harmoni-teal);
  outline-offset: 2px;
}

// High contrast mode support
@media (prefers-contrast: high) {
  .btn-primary {
    border: 2px solid white;
  }

  .card {
    border: 1px solid var(--harmoni-charcoal);
  }
}