# 🔑 HarmoniHSE360 - Seeded User Credentials

> **Quick Reference**: Default login credentials for testing and development

## Demo User Credentials
The application includes seeded demo users for immediate testing:

| Role | Email | Password | Permissions |
|------|-------|----------|-------------|
| **Admin** | <EMAIL> | Admin123! | Full system access |
| **HSE Manager** | <EMAIL> | HSE123! | HSE management |
| **Employee** | <EMAIL> | Employee123! | Basic access |
| **Employee** | <EMAIL> | Employee123! | Basic access |

## 🚀 Quick Login Examples

### Admin Access
```
Email: <EMAIL>
Password: Admin123!
```

### HSE Manager Access  
```
Email: <EMAIL>
Password: HSE123!
```

### Regular Employee Access
```
Email: <EMAIL>
Password: Employee123!
```

## 🔗 Useful Links

### Local Development
- **React Frontend**: http://localhost:5173
- **API Backend**: http://localhost:5000
- **API Swagger**: http://localhost:5000/swagger

### Docker Deployment
- **Full Application**: http://localhost:8080
- **API Swagger**: http://localhost:8080/swagger

### Guides
- **Getting Started**: [Getting_Started_Guide.md](./Getting_Started_Guide.md)
- **Full Authentication Documentation**: [Authentication_Guide.md](./Authentication_Guide.md)

## ⚠️ Important Notes

- **Development Only**: These are demo credentials for development/testing
- **Auto-Fill Available**: The login page displays demo users with auto-fill buttons
- **JWT Tokens**: Valid for 60 minutes (configurable)
- **Role-Based Access**: Currently all authenticated users can access the dashboard
- **Future Enhancement**: Role-specific restrictions will be added for individual modules

---
*For complete setup instructions and troubleshooting, see the [Getting Started Guide](./Getting_Started_Guide.md)*