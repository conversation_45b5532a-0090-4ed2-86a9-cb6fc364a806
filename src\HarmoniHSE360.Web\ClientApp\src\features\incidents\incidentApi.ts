import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

// Types
export interface IncidentDto {
  id: number;
  title: string;
  description: string;
  severity: 'Minor' | 'Moderate' | 'Serious' | 'Critical';
  status: 'Reported' | 'UnderInvestigation' | 'AwaitingAction' | 'Resolved' | 'Closed';
  incidentDate: string;
  location: string;
  reporterId?: number;
  reporterName: string;
  reporterEmail?: string;
  reporterDepartment?: string;
  investigatorId?: number;
  investigatorName?: string;
  createdAt: string;
  lastModifiedAt?: string;
  latitude?: number;
  longitude?: number;
  injuryType?: string;
  medicalTreatmentProvided?: boolean;
  emergencyServicesContacted?: boolean;
  witnessNames?: string;
  immediateActionsTaken?: string;
  attachmentsCount?: number;
  involvedPersonsCount?: number;
  correctiveActionsCount?: number;
  createdBy?: string;
  lastModifiedBy?: string;
}

export interface CreateIncidentRequest {
  title: string;
  description: string;
  severity: 'Minor' | 'Moderate' | 'Serious' | 'Critical';
  incidentDate: string;
  location: string;
  latitude?: number;
  longitude?: number;
  witnessNames?: string;
  immediateActionsTaken?: string;
}

export interface UpdateIncidentRequest {
  title?: string;
  description?: string;
  severity?: 'Minor' | 'Moderate' | 'Serious' | 'Critical';
  status?: 'Reported' | 'UnderInvestigation' | 'AwaitingAction' | 'Resolved' | 'Closed';
  location?: string;
}

export interface IncidentListParams {
  pageNumber?: number;
  pageSize?: number;
  status?: string;
  severity?: string;
  searchTerm?: string;
}

export interface GetIncidentsResponse {
  incidents: IncidentDto[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
}

export interface IncidentStatistics {
  totalIncidents: number;
  openIncidents: number;
  closedIncidents: number;
  criticalIncidents: number;
  incidentsByMonth: Array<{
    month: string;
    count: number;
  }>;
}

// API slice
export const incidentApi = createApi({
  reducerPath: 'incidentApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/incident',
    prepareHeaders: (headers, { getState }) => {
      // Get token from auth state
      const token = (getState() as any).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('content-type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Incident', 'IncidentStatistics'],
  endpoints: (builder) => ({
    // Create incident
    createIncident: builder.mutation<IncidentDto, CreateIncidentRequest>({
      query: (incident) => ({
        url: '',
        method: 'POST',
        body: incident,
      }),
      invalidatesTags: ['Incident', 'IncidentStatistics'],
    }),

    // Get incidents list
    getIncidents: builder.query<GetIncidentsResponse, IncidentListParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        
        if (params.pageNumber) searchParams.append('pageNumber', params.pageNumber.toString());
        if (params.pageSize) searchParams.append('pageSize', params.pageSize.toString());
        if (params.status) searchParams.append('status', params.status);
        if (params.severity) searchParams.append('severity', params.severity);
        if (params.searchTerm) searchParams.append('searchTerm', params.searchTerm);

        return {
          url: `?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Incident',
        ...(result?.incidents.map(({ id }) => ({ type: 'Incident' as const, id })) ?? []),
      ],
    }),

    // Get incident by ID
    getIncident: builder.query<IncidentDto, number>({
      query: (id) => ({
        url: `/${id}`,
        method: 'GET',
      }),
      providesTags: (_, __, id) => [{ type: 'Incident' as const, id }],
    }),

    // Update incident
    updateIncident: builder.mutation<IncidentDto, { id: number; incident: UpdateIncidentRequest }>({
      query: ({ id, incident }) => ({
        url: `/${id}`,
        method: 'PUT',
        body: incident,
      }),
      invalidatesTags: (_, __, { id }) => [
        { type: 'Incident' as const, id },
        'Incident',
        'IncidentStatistics',
      ],
    }),

    // Get my incidents
    getMyIncidents: builder.query<GetIncidentsResponse, IncidentListParams>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        
        if (params.pageNumber) searchParams.append('pageNumber', params.pageNumber.toString());
        if (params.pageSize) searchParams.append('pageSize', params.pageSize.toString());
        if (params.status) searchParams.append('status', params.status);
        if (params.severity) searchParams.append('severity', params.severity);

        return {
          url: `/my-reports?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: ['Incident'],
    }),

    // Get incident statistics
    getIncidentStatistics: builder.query<IncidentStatistics, void>({
      query: () => ({
        url: '/statistics',
        method: 'GET',
      }),
      providesTags: ['IncidentStatistics'],
    }),

    // Delete incident
    deleteIncident: builder.mutation<void, number>({
      query: (id) => ({
        url: `/${id}`,
        method: 'DELETE',
      }),
      // Optimistic update: remove from cache immediately
      onQueryStarted: async (id, { dispatch, queryFulfilled }) => {
        const patches: any[] = [];
        
        try {
          // Update all getIncidents queries optimistically
          const patchResults = dispatch(
            incidentApi.util.updateQueryData('getIncidents', {} as any, (draft) => {
              if (draft && draft.incidents) {
                draft.incidents = draft.incidents.filter(incident => incident.id !== id);
                draft.totalCount = Math.max(0, draft.totalCount - 1);
              }
            })
          );
          patches.push(patchResults);

          // Also handle queries with specific parameters
          [
            { pageNumber: 1, pageSize: 10 },
            { pageNumber: 1, pageSize: 5 },
            { pageNumber: 1, pageSize: 20 },
            { pageNumber: 2, pageSize: 10 },
          ].forEach(params => {
            const patchResult = dispatch(
              incidentApi.util.updateQueryData('getIncidents', params, (draft) => {
                if (draft && draft.incidents) {
                  draft.incidents = draft.incidents.filter(incident => incident.id !== id);
                  draft.totalCount = Math.max(0, draft.totalCount - 1);
                }
              })
            );
            patches.push(patchResult);
          });

          await queryFulfilled;
        } catch {
          // Revert optimistic updates on error
          patches.forEach(patch => patch.undo());
        }
      },
      invalidatesTags: (_, error, id) => {
        if (error) return [];
        return [
          'Incident',
          'IncidentStatistics',
          { type: 'Incident' as const, id },
        ];
      },
    }),

    // Upload incident attachments
    uploadIncidentAttachments: builder.mutation<
      { attachmentIds: number[] },
      { incidentId: number; files: File[] }
    >({
      query: ({ incidentId, files }) => {
        const formData = new FormData();
        files.forEach((file, index) => {
          formData.append(`files[${index}]`, file);
        });

        return {
          url: `/${incidentId}/attachments`,
          method: 'POST',
          body: formData,
          formData: true,
        };
      },
      invalidatesTags: (_, __, { incidentId }) => [
        { type: 'Incident' as const, id: incidentId },
      ],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useCreateIncidentMutation,
  useGetIncidentsQuery,
  useGetIncidentQuery,
  useUpdateIncidentMutation,
  useDeleteIncidentMutation,
  useGetMyIncidentsQuery,
  useGetIncidentStatisticsQuery,
  useUploadIncidentAttachmentsMutation,
} = incidentApi;