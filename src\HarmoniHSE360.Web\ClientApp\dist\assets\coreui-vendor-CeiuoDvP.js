import{g as Fn,r as u,R as d,a as qe,b as pr}from"./react-vendor-Dc0cLFd6.js";var m=function(){return m=Object.assign||function(r){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(r[a]=t[a])}return r},m.apply(this,arguments)};function O(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)r.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(t[n[o]]=e[n[o]]);return t}var fr={exports:{}},Ln="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",In=Ln,Dn=In;function ur(){}function vr(){}vr.resetWarningCache=ur;var jn=function(){function e(n,o,a,s,l,c){if(c!==Dn){var p=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw p.name="Invariant Violation",p}}e.isRequired=e;function r(){return e}var t={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:r,element:e,elementType:e,instanceOf:r,node:e,objectOf:r,oneOf:r,oneOfType:r,shape:r,exact:r,checkPropTypes:vr,resetWarningCache:ur};return t.PropTypes=t,t};fr.exports=jn();var Vn=fr.exports;const i=Fn(Vn);function Bn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var nt={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var Ut;function Hn(){return Ut||(Ut=1,function(e){(function(){var r={}.hasOwnProperty;function t(){for(var a="",s=0;s<arguments.length;s++){var l=arguments[s];l&&(a=o(a,n(l)))}return a}function n(a){if(typeof a=="string"||typeof a=="number")return a;if(typeof a!="object")return"";if(Array.isArray(a))return t.apply(null,a);if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]"))return a.toString();var s="";for(var l in a)r.call(a,l)&&a[l]&&(s=o(s,l));return s}function o(a,s){return s?a?a+" "+s:a+s:a}e.exports?(t.default=t,e.exports=t):window.classNames=t})()}(nt)),nt.exports}var zn=Hn(),x=Bn(zn),mr=u.createContext({}),hr=u.forwardRef(function(e,r){var t=e.children,n=e.activeItemKey,o=e.alwaysOpen,a=o===void 0?!1:o,s=e.className,l=e.flush,c=O(e,["children","activeItemKey","alwaysOpen","className","flush"]),p=u.useState(n),f=p[0],h=p[1];return d.createElement("div",m({className:x("accordion",{"accordion-flush":l},s)},c,{ref:r}),d.createElement(mr.Provider,{value:{_activeItemKey:f,alwaysOpen:a,setActiveKey:h}},t))});hr.propTypes={activeItemKey:i.oneOfType([i.number,i.string]),alwaysOpen:i.bool,children:i.node,className:i.string,flush:i.bool};hr.displayName="CAccordion";function pe(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return u.useMemo(function(){return e.every(function(t){return t==null})?null:function(t){e.forEach(function(n){$n(n,t)})}},e)}function $n(e,r){if(e!=null)if(Mn(e))e(r);else try{e.current=r}catch{throw new Error('Cannot assign value "'.concat(r,'" to ref "').concat(e,'"'))}}function Mn(e){return!!(e&&{}.toString.call(e)=="[object Function]")}var U="top",ee="bottom",te="right",X="left",ut="auto",$e=[U,ee,te,X],Oe="start",Be="end",Wn="clippingParents",br="viewport",Fe="popper",qn="reference",Xt=$e.reduce(function(e,r){return e.concat([r+"-"+Oe,r+"-"+Be])},[]),gr=[].concat($e,[ut]).reduce(function(e,r){return e.concat([r,r+"-"+Oe,r+"-"+Be])},[]),Gn="beforeRead",Kn="read",Un="afterRead",Xn="beforeMain",Yn="main",Jn="afterMain",Zn="beforeWrite",Qn="write",_n="afterWrite",ea=[Gn,Kn,Un,Xn,Yn,Jn,Zn,Qn,_n];function se(e){return e?(e.nodeName||"").toLowerCase():null}function J(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var r=e.ownerDocument;return r&&r.defaultView||window}return e}function ye(e){var r=J(e).Element;return e instanceof r||e instanceof Element}function _(e){var r=J(e).HTMLElement;return e instanceof r||e instanceof HTMLElement}function vt(e){if(typeof ShadowRoot>"u")return!1;var r=J(e).ShadowRoot;return e instanceof r||e instanceof ShadowRoot}function ta(e){var r=e.state;Object.keys(r.elements).forEach(function(t){var n=r.styles[t]||{},o=r.attributes[t]||{},a=r.elements[t];!_(a)||!se(a)||(Object.assign(a.style,n),Object.keys(o).forEach(function(s){var l=o[s];l===!1?a.removeAttribute(s):a.setAttribute(s,l===!0?"":l)}))})}function ra(e){var r=e.state,t={popper:{position:r.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(r.elements.popper.style,t.popper),r.styles=t,r.elements.arrow&&Object.assign(r.elements.arrow.style,t.arrow),function(){Object.keys(r.elements).forEach(function(n){var o=r.elements[n],a=r.attributes[n]||{},s=Object.keys(r.styles.hasOwnProperty(n)?r.styles[n]:t[n]),l=s.reduce(function(c,p){return c[p]="",c},{});!_(o)||!se(o)||(Object.assign(o.style,l),Object.keys(a).forEach(function(c){o.removeAttribute(c)}))})}}const na={name:"applyStyles",enabled:!0,phase:"write",fn:ta,effect:ra,requires:["computeStyles"]};function ie(e){return e.split("-")[0]}var ge=Math.max,Xe=Math.min,Te=Math.round;function lt(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(r){return r.brand+"/"+r.version}).join(" "):navigator.userAgent}function yr(){return!/^((?!chrome|android).)*safari/i.test(lt())}function ke(e,r,t){r===void 0&&(r=!1),t===void 0&&(t=!1);var n=e.getBoundingClientRect(),o=1,a=1;r&&_(e)&&(o=e.offsetWidth>0&&Te(n.width)/e.offsetWidth||1,a=e.offsetHeight>0&&Te(n.height)/e.offsetHeight||1);var s=ye(e)?J(e):window,l=s.visualViewport,c=!yr()&&t,p=(n.left+(c&&l?l.offsetLeft:0))/o,f=(n.top+(c&&l?l.offsetTop:0))/a,h=n.width/o,v=n.height/a;return{width:h,height:v,top:f,right:p+h,bottom:f+v,left:p,x:p,y:f}}function mt(e){var r=ke(e),t=e.offsetWidth,n=e.offsetHeight;return Math.abs(r.width-t)<=1&&(t=r.width),Math.abs(r.height-n)<=1&&(n=r.height),{x:e.offsetLeft,y:e.offsetTop,width:t,height:n}}function Nr(e,r){var t=r.getRootNode&&r.getRootNode();if(e.contains(r))return!0;if(t&&vt(t)){var n=r;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function de(e){return J(e).getComputedStyle(e)}function aa(e){return["table","td","th"].indexOf(se(e))>=0}function fe(e){return((ye(e)?e.ownerDocument:e.document)||window.document).documentElement}function Qe(e){return se(e)==="html"?e:e.assignedSlot||e.parentNode||(vt(e)?e.host:null)||fe(e)}function Yt(e){return!_(e)||de(e).position==="fixed"?null:e.offsetParent}function oa(e){var r=/firefox/i.test(lt()),t=/Trident/i.test(lt());if(t&&_(e)){var n=de(e);if(n.position==="fixed")return null}var o=Qe(e);for(vt(o)&&(o=o.host);_(o)&&["html","body"].indexOf(se(o))<0;){var a=de(o);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||r&&a.willChange==="filter"||r&&a.filter&&a.filter!=="none")return o;o=o.parentNode}return null}function Me(e){for(var r=J(e),t=Yt(e);t&&aa(t)&&de(t).position==="static";)t=Yt(t);return t&&(se(t)==="html"||se(t)==="body"&&de(t).position==="static")?r:t||oa(e)||r}function ht(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Ie(e,r,t){return ge(e,Xe(r,t))}function ia(e,r,t){var n=Ie(e,r,t);return n>t?t:n}function Cr(){return{top:0,right:0,bottom:0,left:0}}function Er(e){return Object.assign({},Cr(),e)}function xr(e,r){return r.reduce(function(t,n){return t[n]=e,t},{})}var sa=function(r,t){return r=typeof r=="function"?r(Object.assign({},t.rects,{placement:t.placement})):r,Er(typeof r!="number"?r:xr(r,$e))};function la(e){var r,t=e.state,n=e.name,o=e.options,a=t.elements.arrow,s=t.modifiersData.popperOffsets,l=ie(t.placement),c=ht(l),p=[X,te].indexOf(l)>=0,f=p?"height":"width";if(!(!a||!s)){var h=sa(o.padding,t),v=mt(a),b=c==="y"?U:X,E=c==="y"?ee:te,g=t.rects.reference[f]+t.rects.reference[c]-s[c]-t.rects.popper[f],y=s[c]-t.rects.reference[c],w=Me(a),P=w?c==="y"?w.clientHeight||0:w.clientWidth||0:0,N=g/2-y/2,C=h[b],R=P-v[f]-h[E],k=P/2-v[f]/2+N,A=Ie(C,k,R),T=c;t.modifiersData[n]=(r={},r[T]=A,r.centerOffset=A-k,r)}}function ca(e){var r=e.state,t=e.options,n=t.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o=="string"&&(o=r.elements.popper.querySelector(o),!o)||Nr(r.elements.popper,o)&&(r.elements.arrow=o))}const da={name:"arrow",enabled:!0,phase:"main",fn:la,effect:ca,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Re(e){return e.split("-")[1]}var pa={top:"auto",right:"auto",bottom:"auto",left:"auto"};function fa(e,r){var t=e.x,n=e.y,o=r.devicePixelRatio||1;return{x:Te(t*o)/o||0,y:Te(n*o)/o||0}}function Jt(e){var r,t=e.popper,n=e.popperRect,o=e.placement,a=e.variation,s=e.offsets,l=e.position,c=e.gpuAcceleration,p=e.adaptive,f=e.roundOffsets,h=e.isFixed,v=s.x,b=v===void 0?0:v,E=s.y,g=E===void 0?0:E,y=typeof f=="function"?f({x:b,y:g}):{x:b,y:g};b=y.x,g=y.y;var w=s.hasOwnProperty("x"),P=s.hasOwnProperty("y"),N=X,C=U,R=window;if(p){var k=Me(t),A="clientHeight",T="clientWidth";if(k===J(t)&&(k=fe(t),de(k).position!=="static"&&l==="absolute"&&(A="scrollHeight",T="scrollWidth")),k=k,o===U||(o===X||o===te)&&a===Be){C=ee;var S=h&&k===R&&R.visualViewport?R.visualViewport.height:k[A];g-=S-n.height,g*=c?1:-1}if(o===X||(o===U||o===ee)&&a===Be){N=te;var F=h&&k===R&&R.visualViewport?R.visualViewport.width:k[T];b-=F-n.width,b*=c?1:-1}}var L=Object.assign({position:l},p&&pa),z=f===!0?fa({x:b,y:g},J(t)):{x:b,y:g};if(b=z.x,g=z.y,c){var D;return Object.assign({},L,(D={},D[C]=P?"0":"",D[N]=w?"0":"",D.transform=(R.devicePixelRatio||1)<=1?"translate("+b+"px, "+g+"px)":"translate3d("+b+"px, "+g+"px, 0)",D))}return Object.assign({},L,(r={},r[C]=P?g+"px":"",r[N]=w?b+"px":"",r.transform="",r))}function ua(e){var r=e.state,t=e.options,n=t.gpuAcceleration,o=n===void 0?!0:n,a=t.adaptive,s=a===void 0?!0:a,l=t.roundOffsets,c=l===void 0?!0:l,p={placement:ie(r.placement),variation:Re(r.placement),popper:r.elements.popper,popperRect:r.rects.popper,gpuAcceleration:o,isFixed:r.options.strategy==="fixed"};r.modifiersData.popperOffsets!=null&&(r.styles.popper=Object.assign({},r.styles.popper,Jt(Object.assign({},p,{offsets:r.modifiersData.popperOffsets,position:r.options.strategy,adaptive:s,roundOffsets:c})))),r.modifiersData.arrow!=null&&(r.styles.arrow=Object.assign({},r.styles.arrow,Jt(Object.assign({},p,{offsets:r.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-placement":r.placement})}const va={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:ua,data:{}};var Ge={passive:!0};function ma(e){var r=e.state,t=e.instance,n=e.options,o=n.scroll,a=o===void 0?!0:o,s=n.resize,l=s===void 0?!0:s,c=J(r.elements.popper),p=[].concat(r.scrollParents.reference,r.scrollParents.popper);return a&&p.forEach(function(f){f.addEventListener("scroll",t.update,Ge)}),l&&c.addEventListener("resize",t.update,Ge),function(){a&&p.forEach(function(f){f.removeEventListener("scroll",t.update,Ge)}),l&&c.removeEventListener("resize",t.update,Ge)}}const ha={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:ma,data:{}};var ba={left:"right",right:"left",bottom:"top",top:"bottom"};function Ue(e){return e.replace(/left|right|bottom|top/g,function(r){return ba[r]})}var ga={start:"end",end:"start"};function Zt(e){return e.replace(/start|end/g,function(r){return ga[r]})}function bt(e){var r=J(e),t=r.pageXOffset,n=r.pageYOffset;return{scrollLeft:t,scrollTop:n}}function gt(e){return ke(fe(e)).left+bt(e).scrollLeft}function ya(e,r){var t=J(e),n=fe(e),o=t.visualViewport,a=n.clientWidth,s=n.clientHeight,l=0,c=0;if(o){a=o.width,s=o.height;var p=yr();(p||!p&&r==="fixed")&&(l=o.offsetLeft,c=o.offsetTop)}return{width:a,height:s,x:l+gt(e),y:c}}function Na(e){var r,t=fe(e),n=bt(e),o=(r=e.ownerDocument)==null?void 0:r.body,a=ge(t.scrollWidth,t.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),s=ge(t.scrollHeight,t.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),l=-n.scrollLeft+gt(e),c=-n.scrollTop;return de(o||t).direction==="rtl"&&(l+=ge(t.clientWidth,o?o.clientWidth:0)-a),{width:a,height:s,x:l,y:c}}function yt(e){var r=de(e),t=r.overflow,n=r.overflowX,o=r.overflowY;return/auto|scroll|overlay|hidden/.test(t+o+n)}function wr(e){return["html","body","#document"].indexOf(se(e))>=0?e.ownerDocument.body:_(e)&&yt(e)?e:wr(Qe(e))}function De(e,r){var t;r===void 0&&(r=[]);var n=wr(e),o=n===((t=e.ownerDocument)==null?void 0:t.body),a=J(n),s=o?[a].concat(a.visualViewport||[],yt(n)?n:[]):n,l=r.concat(s);return o?l:l.concat(De(Qe(s)))}function ct(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Ca(e,r){var t=ke(e,!1,r==="fixed");return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}function Qt(e,r,t){return r===br?ct(ya(e,t)):ye(r)?Ca(r,t):ct(Na(fe(e)))}function Ea(e){var r=De(Qe(e)),t=["absolute","fixed"].indexOf(de(e).position)>=0,n=t&&_(e)?Me(e):e;return ye(n)?r.filter(function(o){return ye(o)&&Nr(o,n)&&se(o)!=="body"}):[]}function xa(e,r,t,n){var o=r==="clippingParents"?Ea(e):[].concat(r),a=[].concat(o,[t]),s=a[0],l=a.reduce(function(c,p){var f=Qt(e,p,n);return c.top=ge(f.top,c.top),c.right=Xe(f.right,c.right),c.bottom=Xe(f.bottom,c.bottom),c.left=ge(f.left,c.left),c},Qt(e,s,n));return l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}function Or(e){var r=e.reference,t=e.element,n=e.placement,o=n?ie(n):null,a=n?Re(n):null,s=r.x+r.width/2-t.width/2,l=r.y+r.height/2-t.height/2,c;switch(o){case U:c={x:s,y:r.y-t.height};break;case ee:c={x:s,y:r.y+r.height};break;case te:c={x:r.x+r.width,y:l};break;case X:c={x:r.x-t.width,y:l};break;default:c={x:r.x,y:r.y}}var p=o?ht(o):null;if(p!=null){var f=p==="y"?"height":"width";switch(a){case Oe:c[p]=c[p]-(r[f]/2-t[f]/2);break;case Be:c[p]=c[p]+(r[f]/2-t[f]/2);break}}return c}function He(e,r){r===void 0&&(r={});var t=r,n=t.placement,o=n===void 0?e.placement:n,a=t.strategy,s=a===void 0?e.strategy:a,l=t.boundary,c=l===void 0?Wn:l,p=t.rootBoundary,f=p===void 0?br:p,h=t.elementContext,v=h===void 0?Fe:h,b=t.altBoundary,E=b===void 0?!1:b,g=t.padding,y=g===void 0?0:g,w=Er(typeof y!="number"?y:xr(y,$e)),P=v===Fe?qn:Fe,N=e.rects.popper,C=e.elements[E?P:v],R=xa(ye(C)?C:C.contextElement||fe(e.elements.popper),c,f,s),k=ke(e.elements.reference),A=Or({reference:k,element:N,placement:o}),T=ct(Object.assign({},N,A)),S=v===Fe?T:k,F={top:R.top-S.top+w.top,bottom:S.bottom-R.bottom+w.bottom,left:R.left-S.left+w.left,right:S.right-R.right+w.right},L=e.modifiersData.offset;if(v===Fe&&L){var z=L[o];Object.keys(F).forEach(function(D){var I=[te,ee].indexOf(D)>=0?1:-1,B=[U,ee].indexOf(D)>=0?"y":"x";F[D]+=z[B]*I})}return F}function wa(e,r){r===void 0&&(r={});var t=r,n=t.placement,o=t.boundary,a=t.rootBoundary,s=t.padding,l=t.flipVariations,c=t.allowedAutoPlacements,p=c===void 0?gr:c,f=Re(n),h=f?l?Xt:Xt.filter(function(E){return Re(E)===f}):$e,v=h.filter(function(E){return p.indexOf(E)>=0});v.length===0&&(v=h);var b=v.reduce(function(E,g){return E[g]=He(e,{placement:g,boundary:o,rootBoundary:a,padding:s})[ie(g)],E},{});return Object.keys(b).sort(function(E,g){return b[E]-b[g]})}function Oa(e){if(ie(e)===ut)return[];var r=Ue(e);return[Zt(e),r,Zt(r)]}function Ta(e){var r=e.state,t=e.options,n=e.name;if(!r.modifiersData[n]._skip){for(var o=t.mainAxis,a=o===void 0?!0:o,s=t.altAxis,l=s===void 0?!0:s,c=t.fallbackPlacements,p=t.padding,f=t.boundary,h=t.rootBoundary,v=t.altBoundary,b=t.flipVariations,E=b===void 0?!0:b,g=t.allowedAutoPlacements,y=r.options.placement,w=ie(y),P=w===y,N=c||(P||!E?[Ue(y)]:Oa(y)),C=[y].concat(N).reduce(function(q,K){return q.concat(ie(K)===ut?wa(r,{placement:K,boundary:f,rootBoundary:h,padding:p,flipVariations:E,allowedAutoPlacements:g}):K)},[]),R=r.rects.reference,k=r.rects.popper,A=new Map,T=!0,S=C[0],F=0;F<C.length;F++){var L=C[F],z=ie(L),D=Re(L)===Oe,I=[U,ee].indexOf(z)>=0,B=I?"width":"height",M=He(r,{placement:L,boundary:f,rootBoundary:h,altBoundary:v,padding:p}),W=I?D?te:X:D?ee:U;R[B]>k[B]&&(W=Ue(W));var Y=Ue(W),G=[];if(a&&G.push(M[z]<=0),l&&G.push(M[W]<=0,M[Y]<=0),G.every(function(q){return q})){S=L,T=!1;break}A.set(L,G)}if(T)for(var ae=E?3:1,le=function(K){var Z=C.find(function(ce){var oe=A.get(ce);if(oe)return oe.slice(0,K).every(function(Ne){return Ne})});if(Z)return S=Z,"break"},ne=ae;ne>0;ne--){var H=le(ne);if(H==="break")break}r.placement!==S&&(r.modifiersData[n]._skip=!0,r.placement=S,r.reset=!0)}}const ka={name:"flip",enabled:!0,phase:"main",fn:Ta,requiresIfExists:["offset"],data:{_skip:!1}};function _t(e,r,t){return t===void 0&&(t={x:0,y:0}),{top:e.top-r.height-t.y,right:e.right-r.width+t.x,bottom:e.bottom-r.height+t.y,left:e.left-r.width-t.x}}function er(e){return[U,te,ee,X].some(function(r){return e[r]>=0})}function Ra(e){var r=e.state,t=e.name,n=r.rects.reference,o=r.rects.popper,a=r.modifiersData.preventOverflow,s=He(r,{elementContext:"reference"}),l=He(r,{altBoundary:!0}),c=_t(s,n),p=_t(l,o,a),f=er(c),h=er(p);r.modifiersData[t]={referenceClippingOffsets:c,popperEscapeOffsets:p,isReferenceHidden:f,hasPopperEscaped:h},r.attributes.popper=Object.assign({},r.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":h})}const Sa={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Ra};function Pa(e,r,t){var n=ie(e),o=[X,U].indexOf(n)>=0?-1:1,a=typeof t=="function"?t(Object.assign({},r,{placement:e})):t,s=a[0],l=a[1];return s=s||0,l=(l||0)*o,[X,te].indexOf(n)>=0?{x:l,y:s}:{x:s,y:l}}function Aa(e){var r=e.state,t=e.options,n=e.name,o=t.offset,a=o===void 0?[0,0]:o,s=gr.reduce(function(f,h){return f[h]=Pa(h,r.rects,a),f},{}),l=s[r.placement],c=l.x,p=l.y;r.modifiersData.popperOffsets!=null&&(r.modifiersData.popperOffsets.x+=c,r.modifiersData.popperOffsets.y+=p),r.modifiersData[n]=s}const Fa={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:Aa};function La(e){var r=e.state,t=e.name;r.modifiersData[t]=Or({reference:r.rects.reference,element:r.rects.popper,placement:r.placement})}const Ia={name:"popperOffsets",enabled:!0,phase:"read",fn:La,data:{}};function Da(e){return e==="x"?"y":"x"}function ja(e){var r=e.state,t=e.options,n=e.name,o=t.mainAxis,a=o===void 0?!0:o,s=t.altAxis,l=s===void 0?!1:s,c=t.boundary,p=t.rootBoundary,f=t.altBoundary,h=t.padding,v=t.tether,b=v===void 0?!0:v,E=t.tetherOffset,g=E===void 0?0:E,y=He(r,{boundary:c,rootBoundary:p,padding:h,altBoundary:f}),w=ie(r.placement),P=Re(r.placement),N=!P,C=ht(w),R=Da(C),k=r.modifiersData.popperOffsets,A=r.rects.reference,T=r.rects.popper,S=typeof g=="function"?g(Object.assign({},r.rects,{placement:r.placement})):g,F=typeof S=="number"?{mainAxis:S,altAxis:S}:Object.assign({mainAxis:0,altAxis:0},S),L=r.modifiersData.offset?r.modifiersData.offset[r.placement]:null,z={x:0,y:0};if(k){if(a){var D,I=C==="y"?U:X,B=C==="y"?ee:te,M=C==="y"?"height":"width",W=k[C],Y=W+y[I],G=W-y[B],ae=b?-T[M]/2:0,le=P===Oe?A[M]:T[M],ne=P===Oe?-T[M]:-A[M],H=r.elements.arrow,q=b&&H?mt(H):{width:0,height:0},K=r.modifiersData["arrow#persistent"]?r.modifiersData["arrow#persistent"].padding:Cr(),Z=K[I],ce=K[B],oe=Ie(0,A[M],q[M]),Ne=N?A[M]/2-ae-oe-Z-F.mainAxis:le-oe-Z-F.mainAxis,j=N?-A[M]/2+ae+oe+ce+F.mainAxis:ne+oe+ce+F.mainAxis,Q=r.elements.arrow&&Me(r.elements.arrow),tt=Q?C==="y"?Q.clientTop||0:Q.clientLeft||0:0,Bt=(D=L==null?void 0:L[C])!=null?D:0,Rn=W+Ne-Bt-tt,Sn=W+j-Bt,Ht=Ie(b?Xe(Y,Rn):Y,W,b?ge(G,Sn):G);k[C]=Ht,z[C]=Ht-W}if(l){var zt,Pn=C==="x"?U:X,An=C==="x"?ee:te,ve=k[R],We=R==="y"?"height":"width",$t=ve+y[Pn],Mt=ve-y[An],rt=[U,X].indexOf(w)!==-1,Wt=(zt=L==null?void 0:L[R])!=null?zt:0,qt=rt?$t:ve-A[We]-T[We]-Wt+F.altAxis,Gt=rt?ve+A[We]+T[We]-Wt-F.altAxis:Mt,Kt=b&&rt?ia(qt,ve,Gt):Ie(b?qt:$t,ve,b?Gt:Mt);k[R]=Kt,z[R]=Kt-ve}r.modifiersData[n]=z}}const Va={name:"preventOverflow",enabled:!0,phase:"main",fn:ja,requiresIfExists:["offset"]};function Ba(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Ha(e){return e===J(e)||!_(e)?bt(e):Ba(e)}function za(e){var r=e.getBoundingClientRect(),t=Te(r.width)/e.offsetWidth||1,n=Te(r.height)/e.offsetHeight||1;return t!==1||n!==1}function $a(e,r,t){t===void 0&&(t=!1);var n=_(r),o=_(r)&&za(r),a=fe(r),s=ke(e,o,t),l={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(n||!n&&!t)&&((se(r)!=="body"||yt(a))&&(l=Ha(r)),_(r)?(c=ke(r,!0),c.x+=r.clientLeft,c.y+=r.clientTop):a&&(c.x=gt(a))),{x:s.left+l.scrollLeft-c.x,y:s.top+l.scrollTop-c.y,width:s.width,height:s.height}}function Ma(e){var r=new Map,t=new Set,n=[];e.forEach(function(a){r.set(a.name,a)});function o(a){t.add(a.name);var s=[].concat(a.requires||[],a.requiresIfExists||[]);s.forEach(function(l){if(!t.has(l)){var c=r.get(l);c&&o(c)}}),n.push(a)}return e.forEach(function(a){t.has(a.name)||o(a)}),n}function Wa(e){var r=Ma(e);return ea.reduce(function(t,n){return t.concat(r.filter(function(o){return o.phase===n}))},[])}function qa(e){var r;return function(){return r||(r=new Promise(function(t){Promise.resolve().then(function(){r=void 0,t(e())})})),r}}function Ga(e){var r=e.reduce(function(t,n){var o=t[n.name];return t[n.name]=o?Object.assign({},o,n,{options:Object.assign({},o.options,n.options),data:Object.assign({},o.data,n.data)}):n,t},{});return Object.keys(r).map(function(t){return r[t]})}var tr={placement:"bottom",modifiers:[],strategy:"absolute"};function rr(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return!r.some(function(n){return!(n&&typeof n.getBoundingClientRect=="function")})}function Ka(e){e===void 0&&(e={});var r=e,t=r.defaultModifiers,n=t===void 0?[]:t,o=r.defaultOptions,a=o===void 0?tr:o;return function(l,c,p){p===void 0&&(p=a);var f={placement:"bottom",orderedModifiers:[],options:Object.assign({},tr,a),modifiersData:{},elements:{reference:l,popper:c},attributes:{},styles:{}},h=[],v=!1,b={state:f,setOptions:function(w){var P=typeof w=="function"?w(f.options):w;g(),f.options=Object.assign({},a,f.options,P),f.scrollParents={reference:ye(l)?De(l):l.contextElement?De(l.contextElement):[],popper:De(c)};var N=Wa(Ga([].concat(n,f.options.modifiers)));return f.orderedModifiers=N.filter(function(C){return C.enabled}),E(),b.update()},forceUpdate:function(){if(!v){var w=f.elements,P=w.reference,N=w.popper;if(rr(P,N)){f.rects={reference:$a(P,Me(N),f.options.strategy==="fixed"),popper:mt(N)},f.reset=!1,f.placement=f.options.placement,f.orderedModifiers.forEach(function(F){return f.modifiersData[F.name]=Object.assign({},F.data)});for(var C=0;C<f.orderedModifiers.length;C++){if(f.reset===!0){f.reset=!1,C=-1;continue}var R=f.orderedModifiers[C],k=R.fn,A=R.options,T=A===void 0?{}:A,S=R.name;typeof k=="function"&&(f=k({state:f,options:T,name:S,instance:b})||f)}}}},update:qa(function(){return new Promise(function(y){b.forceUpdate(),y(f)})}),destroy:function(){g(),v=!0}};if(!rr(l,c))return b;b.setOptions(p).then(function(y){!v&&p.onFirstUpdate&&p.onFirstUpdate(y)});function E(){f.orderedModifiers.forEach(function(y){var w=y.name,P=y.options,N=P===void 0?{}:P,C=y.effect;if(typeof C=="function"){var R=C({state:f,name:w,instance:b,options:N}),k=function(){};h.push(R||k)}})}function g(){h.forEach(function(y){return y()}),h=[]}return b}}var Ua=[ha,Ia,va,na,Fa,ka,Va,da,Sa],Xa=Ka({defaultModifiers:Ua});function dt(){return dt=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},dt.apply(null,arguments)}function Tr(e,r){if(e==null)return{};var t={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(r.includes(n))continue;t[n]=e[n]}return t}function pt(e,r){return pt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,n){return t.__proto__=n,t},pt(e,r)}function kr(e,r){e.prototype=Object.create(r.prototype),e.prototype.constructor=e,pt(e,r)}function Ya(e,r){return e.classList?!!r&&e.classList.contains(r):(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+r+" ")!==-1}function Ja(e,r){e.classList?e.classList.add(r):Ya(e,r)||(typeof e.className=="string"?e.className=e.className+" "+r:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+r))}function nr(e,r){return e.replace(new RegExp("(^|\\s)"+r+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function Za(e,r){e.classList?e.classList.remove(r):typeof e.className=="string"?e.className=nr(e.className,r):e.setAttribute("class",nr(e.className&&e.className.baseVal||"",r))}var ar={disabled:!1},Rr=d.createContext(null),Sr=function(r){return r.scrollTop},Le="unmounted",he="exited",be="entering",we="entered",ft="exiting",re=function(e){kr(r,e);function r(n,o){var a;a=e.call(this,n,o)||this;var s=o,l=s&&!s.isMounting?n.enter:n.appear,c;return a.appearStatus=null,n.in?l?(c=he,a.appearStatus=be):c=we:n.unmountOnExit||n.mountOnEnter?c=Le:c=he,a.state={status:c},a.nextCallback=null,a}r.getDerivedStateFromProps=function(o,a){var s=o.in;return s&&a.status===Le?{status:he}:null};var t=r.prototype;return t.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},t.componentDidUpdate=function(o){var a=null;if(o!==this.props){var s=this.state.status;this.props.in?s!==be&&s!==we&&(a=be):(s===be||s===we)&&(a=ft)}this.updateStatus(!1,a)},t.componentWillUnmount=function(){this.cancelNextCallback()},t.getTimeouts=function(){var o=this.props.timeout,a,s,l;return a=s=l=o,o!=null&&typeof o!="number"&&(a=o.exit,s=o.enter,l=o.appear!==void 0?o.appear:s),{exit:a,enter:s,appear:l}},t.updateStatus=function(o,a){if(o===void 0&&(o=!1),a!==null)if(this.cancelNextCallback(),a===be){if(this.props.unmountOnExit||this.props.mountOnEnter){var s=this.props.nodeRef?this.props.nodeRef.current:qe.findDOMNode(this);s&&Sr(s)}this.performEnter(o)}else this.performExit();else this.props.unmountOnExit&&this.state.status===he&&this.setState({status:Le})},t.performEnter=function(o){var a=this,s=this.props.enter,l=this.context?this.context.isMounting:o,c=this.props.nodeRef?[l]:[qe.findDOMNode(this),l],p=c[0],f=c[1],h=this.getTimeouts(),v=l?h.appear:h.enter;if(!o&&!s||ar.disabled){this.safeSetState({status:we},function(){a.props.onEntered(p)});return}this.props.onEnter(p,f),this.safeSetState({status:be},function(){a.props.onEntering(p,f),a.onTransitionEnd(v,function(){a.safeSetState({status:we},function(){a.props.onEntered(p,f)})})})},t.performExit=function(){var o=this,a=this.props.exit,s=this.getTimeouts(),l=this.props.nodeRef?void 0:qe.findDOMNode(this);if(!a||ar.disabled){this.safeSetState({status:he},function(){o.props.onExited(l)});return}this.props.onExit(l),this.safeSetState({status:ft},function(){o.props.onExiting(l),o.onTransitionEnd(s.exit,function(){o.safeSetState({status:he},function(){o.props.onExited(l)})})})},t.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},t.safeSetState=function(o,a){a=this.setNextCallback(a),this.setState(o,a)},t.setNextCallback=function(o){var a=this,s=!0;return this.nextCallback=function(l){s&&(s=!1,a.nextCallback=null,o(l))},this.nextCallback.cancel=function(){s=!1},this.nextCallback},t.onTransitionEnd=function(o,a){this.setNextCallback(a);var s=this.props.nodeRef?this.props.nodeRef.current:qe.findDOMNode(this),l=o==null&&!this.props.addEndListener;if(!s||l){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var c=this.props.nodeRef?[this.nextCallback]:[s,this.nextCallback],p=c[0],f=c[1];this.props.addEndListener(p,f)}o!=null&&setTimeout(this.nextCallback,o)},t.render=function(){var o=this.state.status;if(o===Le)return null;var a=this.props,s=a.children;a.in,a.mountOnEnter,a.unmountOnExit,a.appear,a.enter,a.exit,a.timeout,a.addEndListener,a.onEnter,a.onEntering,a.onEntered,a.onExit,a.onExiting,a.onExited,a.nodeRef;var l=Tr(a,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return d.createElement(Rr.Provider,{value:null},typeof s=="function"?s(o,l):d.cloneElement(d.Children.only(s),l))},r}(d.Component);re.contextType=Rr;re.propTypes={};function Ce(){}re.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:Ce,onEntering:Ce,onEntered:Ce,onExit:Ce,onExiting:Ce,onExited:Ce};re.UNMOUNTED=Le;re.EXITED=he;re.ENTERING=be;re.ENTERED=we;re.EXITING=ft;var Qa=function(r,t){return r&&t&&t.split(" ").forEach(function(n){return Ja(r,n)})},at=function(r,t){return r&&t&&t.split(" ").forEach(function(n){return Za(r,n)})},Nt=function(e){kr(r,e);function r(){for(var n,o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];return n=e.call.apply(e,[this].concat(a))||this,n.appliedClasses={appear:{},enter:{},exit:{}},n.onEnter=function(l,c){var p=n.resolveArguments(l,c),f=p[0],h=p[1];n.removeClasses(f,"exit"),n.addClass(f,h?"appear":"enter","base"),n.props.onEnter&&n.props.onEnter(l,c)},n.onEntering=function(l,c){var p=n.resolveArguments(l,c),f=p[0],h=p[1],v=h?"appear":"enter";n.addClass(f,v,"active"),n.props.onEntering&&n.props.onEntering(l,c)},n.onEntered=function(l,c){var p=n.resolveArguments(l,c),f=p[0],h=p[1],v=h?"appear":"enter";n.removeClasses(f,v),n.addClass(f,v,"done"),n.props.onEntered&&n.props.onEntered(l,c)},n.onExit=function(l){var c=n.resolveArguments(l),p=c[0];n.removeClasses(p,"appear"),n.removeClasses(p,"enter"),n.addClass(p,"exit","base"),n.props.onExit&&n.props.onExit(l)},n.onExiting=function(l){var c=n.resolveArguments(l),p=c[0];n.addClass(p,"exit","active"),n.props.onExiting&&n.props.onExiting(l)},n.onExited=function(l){var c=n.resolveArguments(l),p=c[0];n.removeClasses(p,"exit"),n.addClass(p,"exit","done"),n.props.onExited&&n.props.onExited(l)},n.resolveArguments=function(l,c){return n.props.nodeRef?[n.props.nodeRef.current,l]:[l,c]},n.getClassNames=function(l){var c=n.props.classNames,p=typeof c=="string",f=p&&c?c+"-":"",h=p?""+f+l:c[l],v=p?h+"-active":c[l+"Active"],b=p?h+"-done":c[l+"Done"];return{baseClassName:h,activeClassName:v,doneClassName:b}},n}var t=r.prototype;return t.addClass=function(o,a,s){var l=this.getClassNames(a)[s+"ClassName"],c=this.getClassNames("enter"),p=c.doneClassName;a==="appear"&&s==="done"&&p&&(l+=" "+p),s==="active"&&o&&Sr(o),l&&(this.appliedClasses[a][s]=l,Qa(o,l))},t.removeClasses=function(o,a){var s=this.appliedClasses[a],l=s.base,c=s.active,p=s.done;this.appliedClasses[a]={},l&&at(o,l),c&&at(o,c),p&&at(o,p)},t.render=function(){var o=this.props;o.classNames;var a=Tr(o,["classNames"]);return d.createElement(re,dt({},a,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},r}(d.Component);Nt.defaultProps={classNames:""};Nt.propTypes={};var Ct=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=e.horizontal,a=e.onHide,s=e.onShow,l=e.visible,c=O(e,["children","className","horizontal","onHide","onShow","visible"]),p=u.useRef(null),f=pe(r,p),h=u.useState(),v=h[0],b=h[1],E=u.useState(),g=E[0],y=E[1],w=function(){if(s&&s(),o){p.current&&y(p.current.scrollWidth);return}p.current&&b(p.current.scrollHeight)},P=function(){if(o){y(0);return}b(0)},N=function(){if(o){p.current&&y(p.current.scrollWidth);return}p.current&&b(p.current.scrollHeight)},C=function(){if(a&&a(),o){y(0);return}b(0)},R=function(){if(o){y(0);return}b(0)};return d.createElement(Nt,{in:l,nodeRef:p,onEntering:w,onEntered:P,onExit:N,onExiting:C,onExited:R,timeout:350},function(k){var A=v===0?null:{height:v},T=g===0?null:{width:g};return d.createElement("div",m({className:x(n,{"collapse-horizontal":o,collapsing:k==="entering"||k==="exiting","collapse show":k==="entered",collapse:k==="exited"}),style:m(m({},A),T)},c,{ref:f}),t)})});Ct.propTypes={children:i.node,className:i.string,horizontal:i.bool,onHide:i.func,onShow:i.func,visible:i.bool};Ct.displayName="CCollapse";var Et=u.createContext({}),Pr=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]),a=u.useContext(Et),s=a.id,l=a.visible;return d.createElement(Ct,{className:"accordion-collapse",id:s,visible:l},d.createElement("div",m({className:x("accordion-body",n)},o,{ref:r}),t))});Pr.propTypes={children:i.node,className:i.string};Pr.displayName="CAccordionBody";var xt=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]),a=u.useContext(Et),s=a.id,l=a.visible,c=a.setVisible;return d.createElement("button",m({type:"button",className:x("accordion-button",{collapsed:!l},n),"aria-controls":s,"aria-expanded":l,onClick:function(){return c(!l)}},o,{ref:r}),t)});xt.propTypes={children:i.node,className:i.string};xt.displayName="CAccordionButton";var Ar=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]);return d.createElement("div",m({className:x("accordion-header",n)},o,{ref:r}),d.createElement(xt,null,t))});Ar.propTypes={children:i.node,className:i.string};Ar.displayName="CAccordionHeader";var Fr=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=e.id,a=e.itemKey,s=O(e,["children","className","id","itemKey"]),l=u.useId(),c=o??l,p=u.useRef(a??c),f=u.useContext(mr),h=f._activeItemKey,v=f.alwaysOpen,b=f.setActiveKey,E=u.useState(h===p.current),g=E[0],y=E[1];return u.useEffect(function(){!v&&g&&b(p.current)},[g]),u.useEffect(function(){y(h===p.current)},[h]),d.createElement("div",m({className:x("accordion-item",n)},s,{ref:r}),d.createElement(Et.Provider,{value:{id:c,setVisible:y,visible:g}},t))});Fr.propTypes={children:i.node,className:i.string,itemKey:i.oneOfType([i.number,i.string])};Fr.displayName="CAccordionItem";var wt=u.forwardRef(function(e,r){var t=e.className,n=e.dark,o=e.disabled,a=e.white,s=O(e,["className","dark","disabled","white"]);return d.createElement("button",m({type:"button",className:x("btn","btn-close",{"btn-close-white":a},o,t),"aria-label":"Close",disabled:o},n&&{"data-coreui-theme":"dark"},s,{ref:r}))});wt.propTypes={className:i.string,dark:i.bool,disabled:i.bool,white:i.bool};wt.displayName="CCloseButton";var $=i.oneOfType([i.oneOf(["primary","secondary","success","danger","warning","info","dark","light"]),i.string]);i.oneOfType([i.arrayOf(i.oneOf(["top","bottom","right","left"]).isRequired),i.oneOf(["top","bottom","right","left"])]);var _a=i.oneOf(["auto","auto-start","auto-end","top-end","top","top-start","bottom-end","bottom","bottom-start","right-start","right","right-end","left-start","left","left-end"]),Lr=i.oneOfType([i.oneOf(["rounded","rounded-top","rounded-end","rounded-bottom","rounded-start","rounded-circle","rounded-pill","rounded-0","rounded-1","rounded-2","rounded-3"]),i.string]),Ir=i.oneOfType([$,i.oneOf(["white","muted"]),i.string]),eo=i.oneOfType([i.arrayOf(i.oneOf(["hover","focus","click"]).isRequired),i.oneOf(["hover","focus","click"])]),Dr=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=e.color,a=o===void 0?"primary":o,s=e.dismissible,l=e.variant,c=e.visible,p=c===void 0?!0:c,f=e.onClose,h=O(e,["children","className","color","dismissible","variant","visible","onClose"]),v=u.useRef(null),b=pe(r,v),E=u.useState(p),g=E[0],y=E[1];return u.useEffect(function(){y(p)},[p]),d.createElement(re,{in:g,mountOnEnter:!0,nodeRef:v,onExit:f,timeout:150,unmountOnExit:!0},function(w){return d.createElement("div",m({className:x("alert",l==="solid"?"bg-".concat(a," text-white"):"alert-".concat(a),{"alert-dismissible fade":s,show:w==="entered"},n),role:"alert"},h,{ref:b}),t,s&&d.createElement(wt,{onClick:function(){return y(!1)}}))})});Dr.propTypes={children:i.node,className:i.string,color:$.isRequired,dismissible:i.bool,onClose:i.func,variant:i.string,visible:i.bool};Dr.displayName="CAlert";var Pe=u.forwardRef(function(e,r){var t=e.children,n=e.active,o=e.as,a=o===void 0?"a":o,s=e.className,l=e.disabled,c=O(e,["children","active","as","className","disabled"]);return d.createElement(a,m({className:x(s,{active:n,disabled:l})},n&&{"aria-current":"page"},a==="a"&&l&&{"aria-disabled":!0,tabIndex:-1},(a==="a"||a==="button")&&{onClick:function(p){p.preventDefault,!l&&c.onClick&&c.onClick(p)}},{disabled:l},c,{ref:r}),t)});Pe.propTypes={active:i.bool,as:i.elementType,children:i.node,className:i.string,disabled:i.bool};Pe.displayName="CLink";var jr=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.color,s=e.shape,l=e.size,c=e.src,p=e.status,f=e.textColor,h=O(e,["children","className","color","shape","size","src","status","textColor"]),v=p&&x("avatar-status","bg-".concat(p));return d.createElement("div",m({className:x("avatar",(t={},t["bg-".concat(a)]=a,t["avatar-".concat(l)]=l,t["text-".concat(f)]=f,t),s,o)},h,{ref:r}),c?d.createElement("img",{src:c,className:"avatar-img"}):n,p&&d.createElement("span",{className:v}))});jr.propTypes={children:i.node,className:i.string,color:$,shape:Lr,size:i.string,src:i.string,status:i.string,textColor:Ir};jr.displayName="CAvatar";var Ot=u.forwardRef(function(e,r){var t=e.className,n=t===void 0?"modal-backdrop":t,o=e.visible,a=O(e,["className","visible"]),s=u.useRef(null),l=pe(r,s);return d.createElement(re,{in:o,mountOnEnter:!0,nodeRef:s,timeout:150,unmountOnExit:!0},function(c){return d.createElement("div",m({className:x(n,"fade",{show:c==="entered"})},a,{ref:l}))})});Ot.propTypes={className:i.string,visible:i.bool};Ot.displayName="CBackdrop";var Vr=u.forwardRef(function(e,r){var t,n=e.children,o=e.as,a=o===void 0?"span":o,s=e.className,l=e.color,c=e.position,p=e.shape,f=e.size,h=e.textBgColor,v=e.textColor,b=O(e,["children","as","className","color","position","shape","size","textBgColor","textColor"]);return d.createElement(a,m({className:x("badge",(t={},t["bg-".concat(l)]=l,t["position-absolute translate-middle"]=c,t["top-0"]=c==null?void 0:c.includes("top"),t["top-100"]=c==null?void 0:c.includes("bottom"),t["start-100"]=c==null?void 0:c.includes("end"),t["start-0"]=c==null?void 0:c.includes("start"),t["badge-".concat(f)]=f,t["text-".concat(v)]=v,t["text-bg-".concat(h)]=h,t),p,s)},b,{ref:r}),n)});Vr.propTypes={as:i.elementType,children:i.node,className:i.string,color:$,position:i.oneOf(["top-start","top-end","bottom-end","bottom-start"]),shape:Lr,size:i.oneOf(["sm"]),textBgColor:$,textColor:Ir};Vr.displayName="CBadge";var Tt=u.forwardRef(function(e,r){var t,n=e.children,o=e.as,a=o===void 0?"button":o,s=e.className,l=e.color,c=e.shape,p=e.size,f=e.type,h=f===void 0?"button":f,v=e.variant,b=O(e,["children","as","className","color","shape","size","type","variant"]);return d.createElement(Pe,m({as:b.href?"a":a},!b.href&&{type:h},{className:x("btn",v&&l?"btn-".concat(v,"-").concat(l):"btn-".concat(v),(t={},t["btn-".concat(l)]=l&&!v,t["btn-".concat(p)]=p,t),c,s)},b,{ref:r}),n)});Tt.propTypes={as:i.elementType,children:i.node,className:i.string,color:$,shape:i.string,size:i.oneOf(["sm","lg"]),type:i.oneOf(["button","submit","reset"]),variant:i.oneOf(["outline","ghost"])};Tt.displayName="CButton";var Br=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.color,s=O(e,["children","className","color"]);return d.createElement("div",m({className:x("callout",(t={},t["callout-".concat(a)]=a,t),o)},s,{ref:r}),n)});Br.propTypes={children:i.node,className:i.string,color:$};Br.displayName="CCallout";var kt=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.color,s=e.textBgColor,l=e.textColor,c=O(e,["children","className","color","textBgColor","textColor"]);return d.createElement("div",m({className:x("card",(t={},t["bg-".concat(a)]=a,t["text-".concat(l)]=l,t["text-bg-".concat(s)]=s,t),o)},c,{ref:r}),n)});kt.propTypes={children:i.node,className:i.string,color:$,textBgColor:$,textColor:i.string};kt.displayName="CCard";var Rt=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]);return d.createElement("div",m({className:x("card-body",n)},o,{ref:r}),t)});Rt.propTypes={children:i.node,className:i.string};Rt.displayName="CCardBody";var Hr=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]);return d.createElement("div",m({className:x("card-group",n)},o,{ref:r}),t)});Hr.propTypes={children:i.node,className:i.string};Hr.displayName="CCardGroup";var zr=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"div":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,m({className:x("card-header",a)},s,{ref:r}),t)});zr.propTypes={as:i.elementType,children:i.node,className:i.string};zr.displayName="CCardHeader";var Ke=function(e){var r=e.getBoundingClientRect();return Math.floor(r.top)>=0&&Math.floor(r.left)>=0&&Math.floor(r.bottom)<=(window.innerHeight||document.documentElement.clientHeight)&&Math.floor(r.right)<=(window.innerWidth||document.documentElement.clientWidth)},to=function(e){return e?typeof e=="function"?e():e:document.body},St=function(e){var r=e.children,t=e.container,n=e.portal,o=u.useState(null),a=o[0],s=o[1];return u.useEffect(function(){n&&s(to(t)||document.body)},[t,n]),typeof window<"u"&&n&&a?pr.createPortal(r,a):d.createElement(d.Fragment,null,r)};St.propTypes={children:i.node,container:i.any,portal:i.bool.isRequired};St.displayName="CConditionalPortal";var Pt=u.createContext({}),ro=function(){var e=u.useRef(void 0),r=u.useRef(null),t=function(a,s,l){e.current=Xa(a,s,l),r.current=s},n=function(){var a=e.current;a&&r.current&&a.destroy(),e.current=void 0},o=function(a){var s=e.current;s&&a&&s.setOptions(a),s&&s.update()};return{popper:e.current,initPopper:t,destroyPopper:n,updatePopper:o}},no=function(e,r,t,n){var o=e.length,a=e.indexOf(r);return a===-1?!t&&n?e[o-1]:e[0]:(a+=t?1:-1,a=(a+o)%o,e[Math.max(0,Math.min(a,o-1))])},ao=function(e){return typeof document<"u"&&document.documentElement.dir==="rtl"?!0:e?e.closest('[dir="rtl"]')!==null:!1},oo=function(e){var r=[];if(typeof e=="object")for(var t in e)r.push("dropdown-menu".concat(t==="xs"?"":"-".concat(t),"-").concat(e[t]));return typeof e=="string"&&r.push("dropdown-menu-".concat(e)),r},io=function(e,r,t,n){var o=e;return r==="dropup"&&(o=n?"top-end":"top-start"),r==="dropup-center"&&(o="top"),r==="dropend"&&(o=n?"left-start":"right-start"),r==="dropstart"&&(o=n?"right-start":"left-start"),t==="end"&&(o=n?"bottom-start":"bottom-end"),o},$r=u.forwardRef(function(e,r){var t,n=e.children,o=e.alignment,a=e.as,s=a===void 0?"div":a,l=e.autoClose,c=l===void 0?!0:l,p=e.className,f=e.container,h=e.dark,v=e.direction,b=e.offset,E=b===void 0?[0,2]:b,g=e.onHide,y=e.onShow,w=e.placement,P=w===void 0?"bottom-start":w,N=e.popper,C=N===void 0?!0:N,R=e.popperConfig,k=e.portal,A=k===void 0?!1:k,T=e.variant,S=T===void 0?"btn-group":T,F=e.visible,L=F===void 0?!1:F,z=O(e,["children","alignment","as","autoClose","className","container","dark","direction","offset","onHide","onShow","placement","popper","popperConfig","portal","variant","visible"]),D=u.useRef(null),I=u.useRef(null),B=u.useRef(null),M=pe(r,D),W=u.useState(L),Y=W[0],G=W[1],ae=ro(),le=ae.initPopper,ne=ae.destroyPopper,H=S==="nav-item"?"li":s;typeof o=="object"&&(C=!1);var q={alignment:o,container:f,dark:h,dropdownToggleRef:I,dropdownMenuRef:B,popper:C,portal:A,variant:S,visible:Y,setVisible:G},K={modifiers:[{name:"offset",options:{offset:E}}],placement:io(P,v,o,ao(B.current))},Z=m(m({},K),typeof R=="function"?R(K):R);u.useEffect(function(){G(L)},[L]),u.useEffect(function(){var j=I.current,Q=B.current;return Y&&j&&Q&&(C&&le(j,Q,Z),j.focus(),j.addEventListener("keydown",ce),Q.addEventListener("keydown",ce),window.addEventListener("mouseup",Ne),window.addEventListener("keyup",oe),y==null||y()),function(){C&&ne(),j==null||j.removeEventListener("keydown",ce),Q==null||Q.removeEventListener("keydown",ce),window.removeEventListener("mouseup",Ne),window.removeEventListener("keyup",oe),g==null||g()}},[Z,ne,B,I,le,Y]);var ce=function(j){if(Y&&B.current&&(j.key==="ArrowDown"||j.key==="ArrowUp")){j.preventDefault();var Q=j.target,tt=Array.from(B.current.querySelectorAll(".dropdown-item:not(.disabled):not(:disabled)"));no(tt,Q,j.key==="ArrowDown",!0).focus()}},oe=function(j){c!==!1&&j.key==="Escape"&&G(!1)},Ne=function(j){if(!(!I.current||!B.current)&&!I.current.contains(j.target)&&(c===!0||c==="inside"&&B.current.contains(j.target)||c==="outside"&&!B.current.contains(j.target))){setTimeout(function(){return G(!1)},1);return}};return d.createElement(Pt.Provider,{value:q},S==="input-group"?d.createElement(d.Fragment,null,n):d.createElement(H,m({className:x(S==="nav-item"?"nav-item dropdown":S,(t={"dropdown-center":v==="center","dropup dropup-center":v==="dropup-center"},t["".concat(v)]=v&&v!=="center"&&v!=="dropup-center",t),p)},z,{ref:M}),n))}),me=i.oneOf(["start","end"]);$r.propTypes={alignment:i.oneOfType([me,i.shape({xs:me.isRequired}),i.shape({sm:me.isRequired}),i.shape({md:me.isRequired}),i.shape({lg:me.isRequired}),i.shape({xl:me.isRequired}),i.shape({xxl:me.isRequired})]),as:i.elementType,autoClose:i.oneOfType([i.bool,i.oneOf(["inside","outside"])]),children:i.node,className:i.string,dark:i.bool,direction:i.oneOf(["center","dropup","dropup-center","dropend","dropstart"]),offset:i.any,onHide:i.func,onShow:i.func,placement:_a,popper:i.bool,popperConfig:i.oneOfType([i.func,i.object]),portal:i.bool,variant:i.oneOf(["btn-group","dropdown","input-group","nav-item"]),visible:i.bool};$r.displayName="CDropdown";var Mr=u.forwardRef(function(e,r){var t=e.className,n=O(e,["className"]);return d.createElement("hr",m({className:x("dropdown-divider",t)},n,{ref:r}))});Mr.propTypes={className:i.string};Mr.displayName="CDropdownDivider";var Wr=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"h6":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,m({className:x("dropdown-header",a)},s,{ref:r}),t)});Wr.propTypes={as:i.elementType,children:i.node,className:i.string};Wr.displayName="CDropdownHeader";var qr=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"a":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(Pe,m({className:x("dropdown-item",a),as:o},s,{ref:r}),t)});qr.propTypes={as:i.elementType,children:i.node,className:i.string};qr.displayName="CDropdownItem";var Gr=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"ul":n,a=e.className,s=O(e,["children","as","className"]),l=u.useContext(Pt),c=l.alignment,p=l.container,f=l.dark,h=l.dropdownMenuRef,v=l.popper,b=l.portal,E=l.visible,g=pe(r,h);return d.createElement(St,{container:p,portal:b??!1},d.createElement(o,m({className:x("dropdown-menu",{show:E},c&&oo(c),a),ref:g,role:"menu"},!v&&{"data-coreui-popper":"static"},f&&{"data-coreui-theme":"dark"},s),o==="ul"?d.Children.map(t,function(y,w){if(d.isValidElement(y))return d.createElement("li",{key:w},d.cloneElement(y))}):t))});Gr.propTypes={as:i.elementType,children:i.node,className:i.string};Gr.displayName="CDropdownMenu";var Kr=function(e){var r=e.children,t=e.caret,n=t===void 0?!0:t,o=e.custom,a=e.className,s=e.navLink,l=s===void 0?!0:s,c=e.split,p=e.trigger,f=p===void 0?"click":p,h=O(e,["children","caret","custom","className","navLink","split","trigger"]),v=u.useContext(Pt),b=v.dropdownToggleRef,E=v.variant,g=v.visible,y=v.setVisible,w=m(m({},(f==="click"||f.includes("click"))&&{onClick:function(C){C.preventDefault(),y(!g)}}),(f==="focus"||f.includes("focus"))&&{onFocus:function(){return y(!0)},onBlur:function(){return y(!1)}}),P=m({className:x({"nav-link":E==="nav-item"&&l,"dropdown-toggle":n,"dropdown-toggle-split":c,show:g},a),"aria-expanded":g},!h.disabled&&m({},w)),N=function(){return o&&d.isValidElement(r)?d.createElement(d.Fragment,null,d.cloneElement(r,m(m({"aria-expanded":g},!h.disabled&&m({},w)),{ref:b}))):E==="nav-item"&&l?d.createElement("a",m({href:"#"},P,{role:"button"},h,{ref:b}),r):d.createElement(Tt,m({},P,{tabIndex:0},h,{ref:b}),r,c&&d.createElement("span",{className:"visually-hidden"},"Toggle Dropdown"))};return d.createElement(N,null)};Kr.propTypes={caret:i.bool,children:i.node,className:i.string,custom:i.bool,split:i.bool,trigger:eo};Kr.displayName="CDropdownToggle";var Ur=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.position,s=O(e,["children","className","position"]);return d.createElement("div",m({className:x("footer",(t={},t["footer-".concat(a)]=a,t),o)},s,{ref:r}),n)});Ur.propTypes={children:i.node,className:i.string,position:i.oneOf(["fixed","sticky"])};Ur.displayName="CFooter";var Xr=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=e.validated,a=O(e,["children","className","validated"]);return d.createElement("form",m({className:x({"was-validated":o},n)||void 0},a,{ref:r}),t)});Xr.propTypes={children:i.node,className:i.string,validated:i.bool};Xr.displayName="CForm";var je=u.forwardRef(function(e,r){var t,n=e.children,o=e.as,a=o===void 0?"div":o,s=e.className,l=e.invalid,c=e.tooltip,p=e.valid,f=O(e,["children","as","className","invalid","tooltip","valid"]);return d.createElement(a,m({className:x((t={},t["invalid-".concat(c?"tooltip":"feedback")]=l,t["valid-".concat(c?"tooltip":"feedback")]=p,t),s)},f,{ref:r}),n)});je.propTypes={as:i.elementType,children:i.node,className:i.string,invalid:i.bool,tooltip:i.bool,valid:i.bool};je.displayName="CFormFeedback";var Ae=function(e){var r=e.describedby,t=e.feedback,n=e.feedbackInvalid,o=e.feedbackValid,a=e.invalid,s=e.tooltipFeedback,l=e.valid;return d.createElement(d.Fragment,null,t&&(l||a)&&d.createElement(je,m({},a&&{id:r},{invalid:a,tooltip:s,valid:l}),t),n&&d.createElement(je,{id:r,invalid:!0,tooltip:s},n),o&&d.createElement(je,{valid:!0,tooltip:s},o))};Ae.propTypes={describedby:i.string,feedback:i.oneOfType([i.node,i.string]),feedbackValid:i.oneOfType([i.node,i.string]),feedbackInvalid:i.oneOfType([i.node,i.string]),invalid:i.bool,tooltipFeedback:i.bool,valid:i.bool};Ae.displayName="CFormControlValidation";var Se=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=e.customClassName,a=O(e,["children","className","customClassName"]);return d.createElement("label",m({className:o??x("form-label",n)},a,{ref:r}),t)});Se.propTypes={children:i.node,className:i.string,customClassName:i.string};Se.displayName="CFormLabel";var Yr=u.forwardRef(function(e,r){var t=e.className,n=e.button,o=e.feedback,a=e.feedbackInvalid,s=e.feedbackValid,l=e.floatingLabel,c=e.tooltipFeedback,p=e.hitArea,f=e.id,h=e.indeterminate,v=e.inline,b=e.invalid,E=e.label,g=e.reverse,y=e.type,w=y===void 0?"checkbox":y,P=e.valid,N=O(e,["className","button","feedback","feedbackInvalid","feedbackValid","floatingLabel","tooltipFeedback","hitArea","id","indeterminate","inline","invalid","label","reverse","type","valid"]),C=u.useRef(null),R=pe(r,C);u.useEffect(function(){C.current&&h&&(C.current.indeterminate=h)},[h,C.current]);var k=function(){return d.createElement("input",m({type:w,className:x(n?"btn-check":"form-check-input",{"is-invalid":b,"is-valid":P,"me-2":p}),id:f},N,{ref:R}))},A=function(){return d.createElement(Ae,{describedby:N["aria-describedby"],feedback:o,feedbackInvalid:a,feedbackValid:s,floatingLabel:l,invalid:b,tooltipFeedback:c,valid:P})},T=function(){var F;return d.createElement(Se,m({customClassName:x(n?x("btn",n.variant?"btn-".concat(n.variant,"-").concat(n.color):"btn-".concat(n.color),(F={},F["btn-".concat(n.size)]=n.size,F),"".concat(n.shape)):"form-check-label")},f&&{htmlFor:f}),E)},S=function(){return n?d.createElement(d.Fragment,null,d.createElement(k,null),E&&d.createElement(T,null),d.createElement(A,null)):E?p?d.createElement(d.Fragment,null,d.createElement(k,null),d.createElement(Se,m({customClassName:x("form-check-label stretched-link",t)},f&&{htmlFor:f}),E),d.createElement(A,null)):d.createElement("div",{className:x("form-check",{"form-check-inline":v,"form-check-reverse":g,"is-invalid":b,"is-valid":P},t)},d.createElement(k,null),d.createElement(T,null),d.createElement(A,null)):d.createElement(k,null)};return d.createElement(S,null)});Yr.propTypes=m({button:i.object,className:i.string,hitArea:i.oneOf(["full"]),id:i.string,indeterminate:i.bool,inline:i.bool,label:i.oneOfType([i.string,i.node]),reverse:i.bool,type:i.oneOf(["checkbox","radio"])},Ae.propTypes);Yr.displayName="CFormCheck";var At=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]);return d.createElement("div",m({className:x("form-floating",n)},o,{ref:r}),t)});At.propTypes={children:i.node,className:i.string};At.displayName="CFormFloating";var Ye=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"div":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,m({className:x("form-text",a)},s,{ref:r}),t)});Ye.propTypes={as:i.elementType,children:i.node,className:i.string};Ye.displayName="CFormText";var ue=function(e){var r=e.children,t=e.describedby,n=e.feedback,o=e.feedbackInvalid,a=e.feedbackValid,s=e.floatingClassName,l=e.floatingLabel,c=e.id,p=e.invalid,f=e.label,h=e.text,v=e.tooltipFeedback,b=e.valid,E=function(){return d.createElement(Ae,{describedby:t,feedback:n,feedbackInvalid:o,feedbackValid:a,floatingLabel:l,invalid:p,tooltipFeedback:v,valid:b})};return l?d.createElement(At,{className:s},r,d.createElement(Se,{htmlFor:c},f||l),h&&d.createElement(Ye,{id:t},h),d.createElement(E,null)):d.createElement(d.Fragment,null,f&&d.createElement(Se,{htmlFor:c},f),r,h&&d.createElement(Ye,{id:t},h),d.createElement(E,null))};ue.propTypes=m({children:i.node,floatingClassName:i.string,floatingLabel:i.oneOfType([i.node,i.string]),label:i.oneOfType([i.node,i.string]),text:i.oneOfType([i.node,i.string])},Ae.propTypes);ue.displayName="CFormControlWrapper";var Jr=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.delay,s=a===void 0?!1:a,l=e.feedback,c=e.feedbackInvalid,p=e.feedbackValid,f=e.floatingClassName,h=e.floatingLabel,v=e.id,b=e.invalid,E=e.label,g=e.onChange,y=e.plainText,w=e.size,P=e.text,N=e.tooltipFeedback,C=e.type,R=C===void 0?"text":C,k=e.valid,A=O(e,["children","className","delay","feedback","feedbackInvalid","feedbackValid","floatingClassName","floatingLabel","id","invalid","label","onChange","plainText","size","text","tooltipFeedback","type","valid"]),T=u.useState(),S=T[0],F=T[1];return u.useEffect(function(){var L=setTimeout(function(){return S&&g&&g(S)},typeof s=="number"?s:500);return function(){return clearTimeout(L)}},[S]),d.createElement(ue,{describedby:A["aria-describedby"],feedback:l,feedbackInvalid:c,feedbackValid:p,floatingClassName:f,floatingLabel:h,id:v,invalid:b,label:E,text:P,tooltipFeedback:N,valid:k},d.createElement("input",m({className:x(y?"form-control-plaintext":"form-control",(t={},t["form-control-".concat(w)]=w,t["form-control-color"]=R==="color",t["is-invalid"]=b,t["is-valid"]=k,t),o),id:v,type:R,onChange:function(L){return s?F(L):g&&g(L)}},A,{ref:r}),n))});Jr.propTypes=m({className:i.string,id:i.string,delay:i.oneOfType([i.bool,i.number]),plainText:i.bool,size:i.oneOf(["sm","lg"]),type:i.oneOfType([i.oneOf(["color","file","text"]),i.string])},ue.propTypes);Jr.displayName="CFormInput";var Zr=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.feedback,s=e.feedbackInvalid,l=e.feedbackValid,c=e.floatingClassName,p=e.floatingLabel,f=e.htmlSize,h=e.id,v=e.invalid,b=e.label,E=e.options,g=e.size,y=e.text,w=e.tooltipFeedback,P=e.valid,N=O(e,["children","className","feedback","feedbackInvalid","feedbackValid","floatingClassName","floatingLabel","htmlSize","id","invalid","label","options","size","text","tooltipFeedback","valid"]);return d.createElement(ue,{describedby:N["aria-describedby"],feedback:a,feedbackInvalid:s,feedbackValid:l,floatingClassName:c,floatingLabel:p,id:h,invalid:v,label:b,text:y,tooltipFeedback:w,valid:P},d.createElement("select",m({id:h,className:x("form-select",(t={},t["form-select-".concat(g)]=g,t["is-invalid"]=v,t["is-valid"]=P,t),o),size:f},N,{ref:r}),E?E.map(function(C,R){return d.createElement("option",m({},typeof C=="object"&&C.disabled&&{disabled:C.disabled},typeof C=="object"&&C.value!==void 0&&{value:C.value},{key:R}),typeof C=="string"?C:C.label)}):n))});Zr.propTypes=m({className:i.string,htmlSize:i.number,options:i.array},ue.propTypes);Zr.displayName="CFormSelect";var Qr=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=e.feedback,a=e.feedbackInvalid,s=e.feedbackValid,l=e.floatingClassName,c=e.floatingLabel,p=e.id,f=e.invalid,h=e.label,v=e.plainText,b=e.text,E=e.tooltipFeedback,g=e.valid,y=O(e,["children","className","feedback","feedbackInvalid","feedbackValid","floatingClassName","floatingLabel","id","invalid","label","plainText","text","tooltipFeedback","valid"]);return d.createElement(ue,{describedby:y["aria-describedby"],feedback:o,feedbackInvalid:a,feedbackValid:s,floatingClassName:l,floatingLabel:c,id:p,invalid:f,label:h,text:b,tooltipFeedback:E,valid:g},d.createElement("textarea",m({className:x(v?"form-control-plaintext":"form-control",{"is-invalid":f,"is-valid":g},n),id:p},y,{ref:r}),t))});Qr.propTypes=m({className:i.string,id:i.string,plainText:i.bool},ue.propTypes);Qr.displayName="CFormTextarea";var _r=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.size,s=O(e,["children","className","size"]);return d.createElement("div",m({className:x("input-group",(t={},t["input-group-".concat(a)]=a,t),o)},s,{ref:r}),n)});_r.propTypes={children:i.node,className:i.string,size:i.oneOf(["sm","lg"])};_r.displayName="CInputGroup";var en=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"span":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,m({className:x("input-group-text",a)},s,{ref:r}),t)});en.propTypes={as:i.elementType,children:i.node,className:i.string};en.displayName="CInputGroupText";var so=["xxl","xl","lg","md","sm","xs"],tn=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]),a=[];return so.forEach(function(s){var l=o[s];delete o[s];var c=s==="xs"?"":"-".concat(s);(typeof l=="number"||typeof l=="string")&&a.push("col".concat(c,"-").concat(l)),typeof l=="boolean"&&a.push("col".concat(c)),l&&typeof l=="object"&&((typeof l.span=="number"||typeof l.span=="string")&&a.push("col".concat(c,"-").concat(l.span)),typeof l.span=="boolean"&&a.push("col".concat(c)),(typeof l.order=="number"||typeof l.order=="string")&&a.push("order".concat(c,"-").concat(l.order)),typeof l.offset=="number"&&a.push("offset".concat(c,"-").concat(l.offset)))}),d.createElement("div",m({className:x(a.length>0?a:"col",n)},o,{ref:r}),t)}),or=i.oneOfType([i.bool,i.number,i.string,i.oneOf(["auto"])]),Ee=i.oneOfType([or,i.shape({span:or,offset:i.oneOfType([i.number,i.string]),order:i.oneOfType([i.oneOf(["first","last"]),i.number,i.string])})]);tn.propTypes={children:i.node,className:i.string,xs:Ee,sm:Ee,md:Ee,lg:Ee,xl:Ee,xxl:Ee};tn.displayName="CCol";var lo=["xxl","xl","lg","md","sm","fluid"],rn=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]),a=[];return lo.forEach(function(s){var l=o[s];delete o[s],l&&a.push("container-".concat(s))}),d.createElement("div",m({className:x(a.length>0?a:"container",n)},o,{ref:r}),t)});rn.propTypes={children:i.node,className:i.string,sm:i.bool,md:i.bool,lg:i.bool,xl:i.bool,xxl:i.bool,fluid:i.bool};rn.displayName="CContainer";var co=["xxl","xl","lg","md","sm","xs"],nn=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]),a=[];return co.forEach(function(s){var l=o[s];delete o[s];var c=s==="xs"?"":"-".concat(s);typeof l=="object"&&(l.cols&&a.push("row-cols".concat(c,"-").concat(l.cols)),typeof l.gutter=="number"&&a.push("g".concat(c,"-").concat(l.gutter)),typeof l.gutterX=="number"&&a.push("gx".concat(c,"-").concat(l.gutterX)),typeof l.gutterY=="number"&&a.push("gy".concat(c,"-").concat(l.gutterY)))}),d.createElement("div",m({className:x("row",a,n)},o,{ref:r}),t)}),xe=i.shape({cols:i.oneOfType([i.oneOf(["auto"]),i.number,i.string]),gutter:i.oneOfType([i.string,i.number]),gutterX:i.oneOfType([i.string,i.number]),gutterY:i.oneOfType([i.string,i.number])});nn.propTypes={children:i.node,className:i.string,xs:xe,sm:xe,md:xe,lg:xe,xl:xe,xxl:xe};nn.displayName="CRow";var an=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.container,s=e.position,l=O(e,["children","className","container","position"]);return d.createElement("div",m({className:x("header",(t={},t["header-".concat(s)]=s,t),o)},l,{ref:r}),a?d.createElement("div",{className:typeof a=="string"?"container-".concat(a):"container"},n):d.createElement(d.Fragment,null,n))});an.propTypes={children:i.node,className:i.string,container:i.oneOfType([i.bool,i.oneOf(["sm","md","lg","xl","xxl","fluid"])]),position:i.oneOf(["fixed","sticky"])};an.displayName="CHeader";var on=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"a":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,m({className:x("header-brand",a)},s,{ref:r}),t)});on.propTypes={as:i.elementType,children:i.node,className:i.string};on.displayName="CHeaderBrand";var sn=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"ul":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,m({className:x("header-nav",a),role:"navigation"},s,{ref:r}),t)});sn.propTypes={as:i.elementType,children:i.node,className:i.string};sn.displayName="CHeaderNav";var ln=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]);return d.createElement("button",m({type:"button",className:x("header-toggler",n)},o,{ref:r}),t??d.createElement("span",{className:"header-toggler-icon"}))});ln.propTypes={children:i.node,className:i.string};ln.displayName="CHeaderToggler";var Ft=u.createContext({}),ir=function(e,r){var t=e.toString().split("."),n=r.toString().split(".");return n.every(function(o,a){return o===t[a]})},cn=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"li":n,a=e.className,s=e.compact,l=e.idx,c=e.toggler,p=e.visible,f=O(e,["children","as","className","compact","idx","toggler","visible"]),h=u.useState(0),v=h[0],b=h[1],E=u.useRef(null),g=u.useContext(Ft),y=g.visibleGroup,w=g.setVisibleGroup,P=u.useState(!!(p||l&&y&&ir(y,l))),N=P[0],C=P[1];u.useEffect(function(){C(!!(l&&y&&ir(y,l)))},[y]);var R=function(I){I.preventDefault(),w(N?l!=null&&l.toString().includes(".")?l.slice(0,l.lastIndexOf(".")):"":l),C(!N)},k={height:0},A=function(){E.current&&b(E.current.scrollHeight)},T=function(){b("auto")},S=function(){E.current&&b(E.current.scrollHeight)},F=function(){var I;(I=E.current)===null||I===void 0||I.offsetHeight,b(0)},L=function(){b(0)},z={entering:{display:"block",height:v},entered:{display:"block",height:v},exiting:{display:"block",height:v},exited:{height:v},unmounted:{}},D=o==="li"?"ul":"div";return d.createElement(o,m({className:x("nav-group",{show:N},a)},f,{ref:r}),c&&d.createElement("a",{className:"nav-link nav-group-toggle",href:"#",onClick:function(I){return R(I)}},c),d.createElement(re,{in:N,nodeRef:E,onEntering:A,onEntered:T,onExit:S,onExiting:F,onExited:L,timeout:300},function(I){return d.createElement(D,{className:x("nav-group-items",{compact:s}),style:m(m({},k),z[I]),ref:E},t)}))});cn.propTypes={as:i.elementType,children:i.node,className:i.string,compact:i.bool,idx:i.string,toggler:i.oneOfType([i.string,i.node]),visible:i.bool};cn.displayName="CNavGroup";var Lt=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=e.idx,a=O(e,["children","className","idx"]),s=u.useRef(null),l=pe(r,s),c=u.useContext(Ft).setVisibleGroup;return u.useEffect(function(){var p;a.active=(p=s.current)===null||p===void 0?void 0:p.classList.contains("active"),o&&a.active&&c(o)},[a.active,n]),d.createElement(Pe,m({className:x("nav-link",n)},a,{ref:l}),t)});Lt.propTypes={active:i.bool,as:i.elementType,children:i.node,className:i.string,disabled:i.bool,idx:i.string};Lt.displayName="CNavLink";var dn=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"li":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,{className:x("nav-item",a),ref:r},s.href||s.to?d.createElement(Lt,m({className:a},s),t):t)});dn.propTypes={as:i.elementType,children:i.node,className:i.string};dn.displayName="CNavItem";var pn=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"li":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,m({className:x("nav-title",a)},s,{ref:r}),t)});pn.propTypes={as:i.elementType,children:i.node,className:i.string};pn.displayName="CNavTitle";var fn=u.forwardRef(function(e,r){var t,n=e.children,o=e.align,a=e.className,s=e.size,l=O(e,["children","align","className","size"]);return d.createElement("nav",m({ref:r},l),d.createElement("ul",{className:x("pagination",(t={},t["justify-content-".concat(o)]=o,t["pagination-".concat(s)]=s,t),a)},n))});fn.propTypes={align:i.oneOf(["start","center","end"]),children:i.node,className:i.string,size:i.oneOf(["sm","lg"])};fn.displayName="CPagination";var un=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=e.className,a=O(e,["children","as","className"]),s=n??(a.active?"span":"a");return d.createElement("li",m({className:x("page-item",{active:a.active,disabled:a.disabled},o)},a.active&&{"aria-current":"page"}),s==="a"?d.createElement(Pe,m({className:"page-link",as:s},a,{ref:r}),t):d.createElement(s,{className:"page-link",ref:r},t))});un.propTypes={as:i.elementType,children:i.node,className:i.string};un.displayName="CPaginationItem";var ot=function(e){return!!getComputedStyle(e).getPropertyValue("--cui-is-mobile")},vn=u.forwardRef(function(e,r){var t,n=e.children,o=e.as,a=o===void 0?"div":o,s=e.className,l=e.colorScheme,c=e.narrow,p=e.onHide,f=e.onShow,h=e.onVisibleChange,v=e.overlaid,b=e.placement,E=e.position,g=e.size,y=e.unfoldable,w=e.visible,P=O(e,["children","as","className","colorScheme","narrow","onHide","onShow","onVisibleChange","overlaid","placement","position","size","unfoldable","visible"]),N=u.useRef(null),C=pe(r,N),R=u.useState(),k=R[0],A=R[1],T=u.useState(!1),S=T[0],F=T[1],L=u.useState(!1),z=L[0],D=L[1],I=u.useState(w!==void 0?w:!v),B=I[0],M=I[1];u.useEffect(function(){N.current&&F(ot(N.current)),w!==void 0&&W(w)},[w]),u.useEffect(function(){k!==void 0&&h&&h(k),!k&&p&&p(),k&&f&&f()},[k]),u.useEffect(function(){S&&D(!1)},[S]),u.useEffect(function(){var H,q;return N.current&&F(ot(N.current)),N.current&&A(Ke(N.current)),window.addEventListener("resize",G),window.addEventListener("mouseup",le),window.addEventListener("keyup",ae),(H=N.current)===null||H===void 0||H.addEventListener("mouseup",ne),(q=N.current)===null||q===void 0||q.addEventListener("transitionend",function(){N.current&&A(Ke(N.current))}),function(){var K,Z;window.removeEventListener("resize",G),window.removeEventListener("mouseup",le),window.removeEventListener("keyup",ae),(K=N.current)===null||K===void 0||K.removeEventListener("mouseup",ne),(Z=N.current)===null||Z===void 0||Z.removeEventListener("transitionend",function(){N.current&&A(Ke(N.current))})}});var W=function(H){if(S){D(H);return}M(H)},Y=function(){W(!1)},G=function(){N.current&&F(ot(N.current)),N.current&&A(Ke(N.current))},ae=function(H){S&&N.current&&!N.current.contains(H.target)&&Y()},le=function(H){S&&N.current&&!N.current.contains(H.target)&&Y()},ne=function(H){var q=H.target;q&&q.classList.contains("nav-link")&&!q.classList.contains("nav-group-toggle")&&S&&Y()};return d.createElement(d.Fragment,null,d.createElement(a,m({className:x("sidebar",(t={},t["sidebar-".concat(l)]=l,t["sidebar-narrow"]=c,t["sidebar-overlaid"]=v,t["sidebar-".concat(b)]=b,t["sidebar-".concat(E)]=E,t["sidebar-".concat(g)]=g,t["sidebar-narrow-unfoldable"]=y,t.show=S&&z||v&&B,t.hide=B===!1&&!S&&!v,t),s)},P,{ref:C}),n),typeof window<"u"&&S&&pr.createPortal(d.createElement(Ot,{className:"sidebar-backdrop",visible:S&&z}),document.body))});vn.propTypes={as:i.elementType,children:i.node,className:i.string,colorScheme:i.oneOf(["dark","light"]),narrow:i.bool,onHide:i.func,onShow:i.func,onVisibleChange:i.func,overlaid:i.bool,placement:i.oneOf(["start","end"]),position:i.oneOf(["fixed","sticky"]),size:i.oneOf(["sm","lg","xl"]),unfoldable:i.bool,visible:i.bool};vn.displayName="CSidebar";var mn=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"a":n,a=e.className,s=O(e,["children","as","className"]);return d.createElement(o,m({className:x("sidebar-brand",a),ref:r},s),t)});mn.propTypes={as:i.elementType,children:i.node,className:i.string};mn.displayName="CSidebarBrand";var hn=u.forwardRef(function(e,r){var t=e.children,n=e.className,o=O(e,["children","className"]);return d.createElement("button",m({className:x("sidebar-toggler",n),ref:r},o),t)});hn.propTypes={children:i.node,className:i.string};hn.displayName="CSidebarToggler";var po=function(e){if(!d.isValidElement(e))return!1;var r=e.type;return r.displayName==="CNavGroup"||r.displayName==="CNavLink"||r.displayName==="CNavItem"},bn=function(e,r,t){return d.Children.map(e,function(n,o){if(!po(n))return n;var a=r?t?"".concat(r,".").concat(o):"".concat(r):"".concat(o);if(n.props.children){var s=n.type,l=s.displayName!=="CNavItem";return d.cloneElement(n,{idx:a,children:bn(n.props.children,a,l)})}return d.cloneElement(n,{idx:a})})},gn=u.forwardRef(function(e,r){var t=e.children,n=e.as,o=n===void 0?"ul":n,a=e.className,s=O(e,["children","as","className"]),l=u.useState(""),c=l[0],p=l[1],f={visibleGroup:c,setVisibleGroup:p};return d.createElement(Ft.Provider,{value:f},d.createElement(o,m({className:x("sidebar-nav",a),ref:r},s),bn(t)))});gn.propTypes={as:i.elementType,children:i.node,className:i.string};gn.displayName="CSidebarNav";var yn=u.forwardRef(function(e,r){var t,n=e.as,o=n===void 0?"div":n,a=e.className,s=e.color,l=e.size,c=e.variant,p=c===void 0?"border":c,f=e.visuallyHiddenLabel,h=f===void 0?"Loading...":f,v=O(e,["as","className","color","size","variant","visuallyHiddenLabel"]);return d.createElement(o,m({className:x("spinner-".concat(p),(t={},t["spinner-".concat(p,"-").concat(l)]=l,t["text-".concat(s)]=s,t),a),role:"status"},v,{ref:r}),d.createElement("span",{className:"visually-hidden"},h))});yn.propTypes={as:i.string,className:i.string,color:$,size:i.oneOf(["sm"]),variant:i.oneOf(["border","grow"]),visuallyHiddenLabel:i.string};yn.displayName="CSpinner";var _e=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.color,s=O(e,["children","className","color"]);return d.createElement("thead",m({className:x((t={},t["table-".concat(a)]=a,t),o)||void 0},s,{ref:r}),n)});_e.propTypes={children:i.node,className:i.string,color:$};_e.displayName="CTableHead";var It=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.color,s=O(e,["children","className","color"]);return d.createElement("th",m({className:x((t={},t["table-".concat(a)]=a,t),o)||void 0},s,{ref:r}),n)});It.propTypes={children:i.node,className:i.string,color:$};It.displayName="CTableHeaderCell";var Dt=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.color,s=O(e,["children","className","color"]);return d.createElement("tbody",m({className:x((t={},t["table-".concat(a)]=a,t),o)||void 0},s,{ref:r}),n)});Dt.propTypes={children:i.node,className:i.string,color:$};Dt.displayName="CTableBody";var Je=u.forwardRef(function(e,r){var t,n=e.children,o=e.active,a=e.align,s=e.className,l=e.color,c=O(e,["children","active","align","className","color"]),p=c.scope?"th":"td";return d.createElement(p,m({className:x((t={},t["align-".concat(a)]=a,t["table-active"]=o,t["table-".concat(l)]=l,t),s)||void 0},c,{ref:r}),n)});Je.propTypes={active:i.bool,align:i.oneOf(["bottom","middle","top"]),children:i.node,className:i.string,color:$};Je.displayName="CTableDataCell";var Ve=u.forwardRef(function(e,r){var t,n=e.children,o=e.active,a=e.align,s=e.className,l=e.color,c=O(e,["children","active","align","className","color"]);return d.createElement("tr",m({className:x((t={},t["align-".concat(a)]=a,t["table-active"]=o,t["table-".concat(l)]=l,t),s)||void 0},c,{ref:r}),n)});Ve.propTypes={active:i.bool,align:i.oneOf(["bottom","middle","top"]),children:i.node,className:i.string,color:$};Ve.displayName="CTableRow";var et=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.color,s=O(e,["children","className","color"]);return d.createElement("tfoot",m({className:x((t={},t["table-".concat(a)]=a,t),o)||void 0},s,{ref:r}),n)});et.propTypes={children:i.node,className:i.string,color:$};et.displayName="CTableFoot";var jt=u.forwardRef(function(e,r){var t=e.children,n=O(e,["children"]);return d.createElement("caption",m({},n,{ref:r}),t)});jt.propTypes={children:i.node};jt.displayName="CTableCaption";var Vt=function(e){var r=e.children,t=e.responsive,n=O(e,["children","responsive"]);return t?d.createElement("div",m({className:typeof t=="boolean"?"table-responsive":"table-responsive-".concat(t)},n),r):d.createElement(d.Fragment,null,r)};Vt.propTypes={children:i.node,responsive:i.oneOfType([i.bool,i.oneOf(["sm","md","lg","xl","xxl"])])};Vt.displayName="CTableResponsiveWrapper";var sr=function(e){return e.replace(/[-_.]/g," ").replace(/ +/g," ").replace(/([a-z0-9])([A-Z])/g,"$1 $2").split(" ").map(function(r){return r.charAt(0).toUpperCase()+r.slice(1)}).join(" ")},fo=function(e){var r;return typeof e=="object"?(r=e.label)!==null&&r!==void 0?r:sr(e.key):sr(e)},uo=function(e,r){return e?e.map(function(t){return typeof t=="object"?t.key:t}):r&&vo(r)},vo=function(e){return Object.keys(e[0]||{}).filter(function(r){return r.charAt(0)!=="_"})},Nn=u.forwardRef(function(e,r){var t,n=e.children,o=e.align,a=e.borderColor,s=e.bordered,l=e.borderless,c=e.caption,p=e.captionTop,f=e.className,h=e.color,v=e.columns,b=e.footer,E=e.hover,g=e.items,y=e.responsive,w=e.small,P=e.striped,N=e.stripedColumns,C=e.tableFootProps,R=e.tableHeadProps,k=O(e,["children","align","borderColor","bordered","borderless","caption","captionTop","className","color","columns","footer","hover","items","responsive","small","striped","stripedColumns","tableFootProps","tableHeadProps"]),A=u.useMemo(function(){return uo(v,g)},[v,g]);return d.createElement(Vt,{responsive:y},d.createElement("table",m({className:x("table",(t={},t["align-".concat(o)]=o,t["border-".concat(a)]=a,t["caption-top"]=p||c==="top",t["table-bordered"]=s,t["table-borderless"]=l,t["table-".concat(h)]=h,t["table-hover"]=E,t["table-sm"]=w,t["table-striped"]=P,t["table-striped-columns"]=N,t),f)},k,{ref:r}),(c&&c!=="top"||p)&&d.createElement(jt,null,c||p),v&&d.createElement(_e,m({},R),d.createElement(Ve,null,v.map(function(T,S){return d.createElement(It,m({},T._props&&m({},T._props),T._style&&{style:m({},T._style)},{key:S}),fo(T))}))),g&&d.createElement(Dt,null,g.map(function(T,S){return d.createElement(Ve,m({},T._props&&m({},T._props),{key:S}),A&&A.map(function(F,L){return T[F]!==void 0?d.createElement(Je,m({},T._cellProps&&m(m({},T._cellProps.all&&m({},T._cellProps.all)),T._cellProps[F]&&m({},T._cellProps[F])),{key:L}),T[F]):null}))})),n,b&&d.createElement(et,m({},C),d.createElement(Ve,null,b.map(function(T,S){return d.createElement(Je,m({},typeof T=="object"&&T._props&&m({},T._props),{key:S}),typeof T=="object"?T.label:T)})))))});Nn.propTypes={align:i.oneOf(["bottom","middle","top"]),borderColor:i.string,bordered:i.bool,borderless:i.bool,caption:i.oneOfType([i.string,i.oneOf(["top"])]),captionTop:i.string,children:i.node,className:i.string,color:$,columns:i.array,footer:i.array,hover:i.bool,items:i.array,responsive:i.oneOfType([i.bool,i.oneOf(["sm","md","lg","xl","xxl"])]),small:i.bool,striped:i.bool,stripedColumns:i.bool,tableFootProps:i.shape(m({},et.propTypes)),tableHeadProps:i.shape(m({},_e.propTypes))};Nn.displayName="CTable";var Cn=u.forwardRef(function(e,r){var t,n=e.action,o=e.chart,a=e.className,s=e.color,l=e.title,c=e.value,p=O(e,["action","chart","className","color","title","value"]);return d.createElement(kt,m({className:x((t={},t["bg-".concat(s)]=s,t["text-white"]=s,t),a)},p,{ref:r}),d.createElement(Rt,{className:"pb-0 d-flex justify-content-between align-items-start"},d.createElement("div",null,c&&d.createElement("div",{className:"fs-4 fw-semibold"},c),l&&d.createElement("div",null,l)),n),o)});Cn.propTypes={action:i.node,chart:i.oneOfType([i.string,i.node]),className:i.string,color:$,title:i.oneOfType([i.string,i.node]),value:i.oneOfType([i.string,i.node,i.number])};Cn.displayName="CWidgetStatsA";var lr={},ze=function(){return ze=Object.assign||function(r){for(var t,n=1,o=arguments.length;n<o;n++){t=arguments[n];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(r[a]=t[a])}return r},ze.apply(this,arguments)};function En(e,r){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&r.indexOf(n)<0&&(t[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)r.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(t[n[o]]=e[n[o]]);return t}function xn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var wn={exports:{}},it,cr;function mo(){if(cr)return it;cr=1;var e="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return it=e,it}var st,dr;function ho(){if(dr)return st;dr=1;var e=mo();function r(){}function t(){}return t.resetWarningCache=r,st=function(){function n(s,l,c,p,f,h){if(h!==e){var v=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw v.name="Invariant Violation",v}}n.isRequired=n;function o(){return n}var a={array:n,bigint:n,bool:n,func:n,number:n,object:n,string:n,symbol:n,any:n,arrayOf:o,element:n,elementType:n,instanceOf:o,node:n,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:t,resetWarningCache:r};return a.PropTypes=a,a},st}wn.exports=ho()();var bo=wn.exports,V=xn(bo),On={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(e){(function(){var r={}.hasOwnProperty;function t(){for(var a="",s=0;s<arguments.length;s++){var l=arguments[s];l&&(a=o(a,n(l)))}return a}function n(a){if(typeof a=="string"||typeof a=="number")return a;if(typeof a!="object")return"";if(Array.isArray(a))return t.apply(null,a);if(a.toString!==Object.prototype.toString&&!a.toString.toString().includes("[native code]"))return a.toString();var s="";for(var l in a)r.call(a,l)&&a[l]&&(s=o(s,l));return s}function o(a,s){return s?a?a+" "+s:a+s:a}e.exports?(t.default=t,e.exports=t):window.classNames=t})()})(On);var go=On.exports,Ze=xn(go),yo=function(e){return e.replace(/([-_][a-z0-9])/gi,function(r){return r.toUpperCase()}).replace(/-/gi,"")},Tn=u.forwardRef(function(e,r){var t,n=e.className,o=e.content,a=e.customClassName,s=e.height,l=e.icon,c=e.name,p=e.size,f=e.title,h=e.use,v=e.width,b=En(e,["className","content","customClassName","height","icon","name","size","title","use","width"]),E=u.useState(0),g=E[0],y=E[1],w=l||o||c;o&&process,c&&process,u.useMemo(function(){return y(g+1)},[w,JSON.stringify(w)]);var P=f?"<title>".concat(f,"</title>"):"",N=u.useMemo(function(){var T=w&&typeof w=="string"&&w.includes("-")?yo(w):w;if(Array.isArray(w))return w;if(typeof w=="string"&&d.icons)return d[T]},[g]),C=u.useMemo(function(){return Array.isArray(N)?N[1]||N[0]:N},[g]),R=function(){return Array.isArray(N)&&N.length>1?N[0]:"64 64"}(),k=function(){return b.viewBox||"0 0 ".concat(R)}(),A=a?Ze(a):Ze("icon",(t={},t["icon-".concat(p)]=p,t["icon-custom-size"]=s||v,t),n);return d.createElement(d.Fragment,null,h?d.createElement("svg",ze({xmlns:"http://www.w3.org/2000/svg",className:A},s&&{height:s},v&&{width:v},{role:"img","aria-hidden":"true"},b,{ref:r}),d.createElement("use",{href:h})):d.createElement("svg",ze({xmlns:"http://www.w3.org/2000/svg",viewBox:k,className:A},s&&{height:s},v&&{width:v},{role:"img","aria-hidden":"true",dangerouslySetInnerHTML:{__html:P+C}},b,{ref:r})),f&&d.createElement("span",{className:"visually-hidden"},f))});Tn.propTypes={className:V.string,content:V.oneOfType([V.array,V.string]),customClassName:V.string,height:V.number,icon:V.oneOfType([V.array,V.string]),name:V.string,size:V.oneOf(["custom","custom-size","sm","lg","xl","xxl","3xl","4xl","5xl","6xl","7xl","8xl","9xl"]),title:V.string,use:V.string,viewBox:V.string,width:V.number};Tn.displayName="CIcon";var kn=u.forwardRef(function(e,r){var t,n=e.children,o=e.className,a=e.customClassName,s=e.height,l=e.size,c=e.title,p=e.width,f=En(e,["children","className","customClassName","height","size","title","width"]),h=a?Ze(a):Ze("icon",(t={},t["icon-".concat(l)]=l,t["icon-custom-size"]=s||p,t),o);return d.createElement(d.Fragment,null,u.Children.map(n,function(v){if(d.isValidElement(v))return d.cloneElement(v,ze({"aria-hidden":!0,className:h,focusable:"false",ref:r,role:"img"},f))}),c&&d.createElement("span",{className:"visually-hidden"},c))});kn.propTypes={className:V.string,customClassName:V.string,height:V.number,size:V.oneOf(["custom","custom-size","sm","lg","xl","xxl","3xl","4xl","5xl","6xl","7xl","8xl","9xl"]),title:V.string,width:V.number};kn.displayName="CIconSvg";export{Cn as A,kt as B,vn as C,zr as D,Rt as E,Hr as F,Xr as G,_r as H,en as I,Jr as J,Yr as K,Br as L,hr as M,Fr as N,Ar as O,Pr as P,Zr as Q,Nn as R,_e as S,Ve as T,It as U,Dt as V,Je as W,fn as X,un as Y,Se as Z,Qr as _,mn as a,gn as b,dn as c,pn as d,cn as e,hn as f,an as g,rn as h,ln as i,Tn as j,on as k,sn as l,$r as m,Kr as n,Vr as o,Gr as p,Wr as q,qr as r,Mr as s,jr as t,Ur as u,yn as v,Dr as w,Tt as x,nn as y,tn as z};
