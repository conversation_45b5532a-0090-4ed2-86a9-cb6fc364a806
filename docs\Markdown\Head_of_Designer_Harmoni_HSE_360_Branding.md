# 🎯 Branding Summary: HarmoniHSE360

## 🏷️ Tagline Options

These focus on key values: safety, harmony, community, and completeness.

### Professional Tone

* "Complete Safety. Seamless Harmony."
* "360° Coverage for a Safer School Environment."
* "Empowering Safer Learning, Every Day."

### Inclusive/Community-Focused

* "Together for Safety, In Every Step."
* "Your School. Your Safety. Our Harmony."
* "Satu <PERSON>. Satu <PERSON>i. 360 Keamanan." (Bilingual option for local culture resonance)

---

## 🎨 Logo Concept for HarmoniHSE360

### 1. Shape & Style

* **Circular motif**: Represents the "360" idea of full coverage, unity, and harmony.
* **Shield or checkmark overlay**: Symbolizes safety, protection, and compliance.
* **Three interlinked elements**: Represent H (Health), S (Safety), and E (Environment) — possibly in a triangular rotation or yin-yang inspired flow.

### 2. Color Palette

* **Teal/Blue** – Trust, professionalism, calm (common in HSE and educational platforms)
* **Green** – Environment, growth, sustainability
* **Warm yellow or orange accents** – Visibility, action, and alertness

### 3. Typography

* Clean, sans-serif font (e.g., **Inter**, **Poppins**, **Montserrat**)
* "**Harmoni**" in bold, "**HSE360**" in lighter weight for contrast
  *e.g.*: Harmoni *HSE360*

### 4. Imagery Suggestions for Designer

* Include subtle **leaf elements** to represent environmental care
* **Mobile-friendly icon** version: just the "H" in a circle with a shield or wave motif
* **Optional bilingual lockup**:
  **HarmoniHSE360**
  *"Platform Keselamatan & Kesehatan Sekolah"*

---

## 🧭 HarmoniHSE360 Style Guide for Developers

### 🎨 Color Palette

| Color         | Purpose                       | Hex       | RGB                |
| ------------- | ----------------------------- | --------- | ------------------ |
| Teal Primary  | Primary brand color, trust    | `#0097A7` | rgb(0, 151, 167)   |
| Deep Blue     | Professionalism, stability    | `#004D6E` | rgb(0, 77, 110)    |
| Leaf Green    | Environment, growth           | `#66BB6A` | rgb(102, 187, 106) |
| Accent Yellow | Alerts, attention, visibility | `#F9A825` | rgb(249, 168, 37)  |
| Soft Grey     | Backgrounds and UI support    | `#F5F5F5` | rgb(245, 245, 245) |
| Charcoal      | Text                          | `#212121` | rgb(33, 33, 33)    |

Use high contrast combinations for accessibility (WCAG 2.1 AA).

### 🔠 Typography

* **Primary Font**: [Inter](https://fonts.google.com/specimen/Inter)

  * UI elements, forms, buttons, and body text
* **Alternate Font**: [Poppins](https://fonts.google.com/specimen/Poppins)

  * Branding headlines, large banners, highlights

| Text Style     | Font Weight | Size                        | Usage                           |
| -------------- | ----------- | --------------------------- | ------------------------------- |
| Display Title  | Bold        | 32–48px                     | Dashboard titles, headers       |
| Section Header | SemiBold    | 24–30px                     | Form sections, page blocks      |
| Body Text      | Regular     | 16–18px                     | Paragraphs, labels, table text  |
| Caption        | Medium      | 12–14px                     | Notes, helper text, disclaimers |
| Button Text    | Medium      | 14–16px (all caps optional) | All CTA and form buttons        |

### 🧩 UI Components Style

* **Buttons**

  * Primary: Teal background with white text
  * Secondary: Transparent or soft grey with teal border/text
  * Danger/Alert: Yellow or red tone as appropriate
  * Rounded corners: `8px` radius

* **Cards and Containers**

  * Background: `#FFFFFF` or `#F5F5F5`
  * Shadow: Soft drop shadow (`box-shadow: 0 2px 6px rgba(0,0,0,0.1)`)
  * Padding: `16px`–`24px`

* **Forms & Inputs**

  * Input border: `1px solid #CCCCCC`, focused state: `1px solid #0097A7`
  * Label font: 14px medium, always above the input
  * Placeholder font: 14px light grey

* **Icons**

  * Use [Lucide](https://lucide.dev/) or \[Material Icons Rounded]
  * Icon color: Teal or Charcoal
  * Icon size: 20px–24px

### 📱 Mobile Considerations

* Minimum touch target: `44x44px`
* Responsive breakpoints:

  * Small: ≤ 600px (phones)
  * Medium: 600–1024px (tablets)
  * Large: ≥ 1024px (desktops)
* Ensure offline indicators and sync status are visible
* Use swipeable cards and accordion-style collapsibles

### 🔒 Accessibility (WCAG 2.1 AA)

* Minimum 4.5:1 contrast ratio for text
* All interactive elements must be keyboard accessible
* Add `aria-labels` to custom controls
* Support dark mode and high contrast mode

### 🔗 Iconography & Imagery

* Use flat, minimal, positive-tone iconography
* Include nature, safety, and community themes
* Avoid excessive realism or skeuomorphism
* Stick to line icons or flat SVG illustrations

### 🧪 Testing and QA Notes

* Test in Chrome, Firefox, Safari, and Edge (latest versions)
* Test dark/light mode appearance
* Validate responsiveness on both Android/iOS
* Run accessibility audits via Lighthouse or Axe

---

**Note for Developers**: This style guide reflects the core branding and design principles of HarmoniHSE360. Follow these standards to ensure a consistent, professional, and user-centric experience throughout the app.
