using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MediatR;
using HarmoniHSE360.Application.Features.Incidents.Commands;
using HarmoniHSE360.Application.Features.Incidents.Queries;
using HarmoniHSE360.Application.Features.Incidents.DTOs;
using HarmoniHSE360.Application.Common.Interfaces;
using HarmoniHSE360.Domain.Entities;

namespace HarmoniHSE360.Web.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class IncidentController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ICurrentUserService _currentUserService;
    private readonly ILogger<IncidentController> _logger;

    public IncidentController(
        IMediator mediator,
        ICurrentUserService currentUserService,
        ILogger<IncidentController> logger)
    {
        _mediator = mediator;
        _currentUserService = currentUserService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new incident report
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(IncidentDto), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<IncidentDto>> CreateIncident([FromBody] CreateIncidentRequest request)
    {
        try
        {
            // Parse enum value from string
            if (!Enum.TryParse<IncidentSeverity>(request.Severity, out var severity))
            {
                return BadRequest(new { message = $"Invalid severity value: {request.Severity}" });
            }

            var command = new CreateIncidentCommand
            {
                Title = request.Title,
                Description = request.Description,
                Severity = severity,
                IncidentDate = request.IncidentDate,
                Location = request.Location,
                Latitude = request.Latitude,
                Longitude = request.Longitude,
                WitnessNames = request.WitnessNames,
                ImmediateActionsTaken = request.ImmediateActionsTaken,
                ReporterId = _currentUserService.UserId
            };

            var result = await _mediator.Send(command);

            _logger.LogInformation("Incident created successfully with ID: {IncidentId}", result.Id);

            return CreatedAtAction(nameof(GetIncident), new { id = result.Id }, result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating incident for user {UserId}", _currentUserService.UserId);
            return BadRequest(new { message = "An error occurred while creating the incident" });
        }
    }

    /// <summary>
    /// Get incident by ID
    /// </summary>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(IncidentDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<IncidentDto>> GetIncident(int id)
    {
        try
        {
            _logger.LogInformation("Getting incident {IncidentId} for user {UserId}", id, _currentUserService.UserId);

            var query = new GetIncidentByIdQuery { Id = id };
            var result = await _mediator.Send(query);
            
            if (result == null)
            {
                return NotFound(new { message = "Incident not found or access denied" });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving incident {IncidentId}", id);
            return BadRequest(new { message = "An error occurred while retrieving the incident" });
        }
    }

    /// <summary>
    /// Get all incidents (with pagination and filtering)
    /// </summary>
    [HttpGet]
    [ProducesResponseType(typeof(GetIncidentsResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<GetIncidentsResponse>> GetIncidents(
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? status = null,
        [FromQuery] string? severity = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var query = new GetIncidentsQuery
            {
                SearchTerm = searchTerm,
                Status = status,
                Severity = severity,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            _logger.LogInformation("Getting incidents for user {UserId} with filters: {@Query}",
                _currentUserService.UserId, query);

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving incidents for user {UserId}", _currentUserService.UserId);
            return BadRequest(new { message = "An error occurred while retrieving incidents" });
        }
    }

    /// <summary>
    /// Update incident status or details
    /// </summary>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(IncidentDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<IncidentDto>> UpdateIncident(int id, [FromBody] UpdateIncidentRequest request)
    {
        try
        {
            _logger.LogInformation("Updating incident {IncidentId} by user {UserId}", id, _currentUserService.UserId);
            _logger.LogInformation("Request data: Title={Title}, Description={Description}, Severity={Severity}, Status={Status}, Location={Location}", 
                request?.Title, request?.Description, request?.Severity, request?.Status, request?.Location);
            
            if (request == null)
            {
                return BadRequest(new { message = "Request body is required" });
            }
            
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("ModelState validation failed: {@ModelState}", ModelState);
                return BadRequest(ModelState);
            }

            // Parse enum values from strings
            if (!Enum.TryParse<IncidentSeverity>(request.Severity, out var severity))
            {
                severity = IncidentSeverity.Minor;
            }
            
            if (!Enum.TryParse<IncidentStatus>(request.Status, out var status))
            {
                status = IncidentStatus.Reported;
            }

            var command = new UpdateIncidentCommand
            {
                Id = id,
                Title = request.Title ?? string.Empty,
                Description = request.Description ?? string.Empty,
                Severity = severity,
                Status = status,
                Location = request.Location ?? string.Empty
            };

            var result = await _mediator.Send(command);
            
            if (result == null)
            {
                return NotFound(new { message = "Incident not found or access denied" });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating incident {IncidentId}", id);
            return BadRequest(new { message = "An error occurred while updating the incident" });
        }
    }

    /// <summary>
    /// Get incidents reported by current user
    /// </summary>
    [HttpGet("my-reports")]
    [ProducesResponseType(typeof(GetIncidentsResponse), StatusCodes.Status200OK)]
    public async Task<ActionResult<GetIncidentsResponse>> GetMyReports(
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? status = null,
        [FromQuery] string? severity = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
    {
        try
        {
            var query = new GetMyIncidentsQuery
            {
                SearchTerm = searchTerm,
                Status = status,
                Severity = severity,
                PageNumber = pageNumber,
                PageSize = pageSize,
                UserId = _currentUserService.UserId
            };

            _logger.LogInformation("Getting my incidents for user {UserId} with filters: {@Query}",
                _currentUserService.UserId, query);

            var result = await _mediator.Send(query);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving my incidents for user {UserId}", _currentUserService.UserId);
            return BadRequest(new { message = "An error occurred while retrieving your incidents" });
        }
    }

    /// <summary>
    /// Delete an incident
    /// </summary>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> DeleteIncident(int id)
    {
        try
        {
            _logger.LogInformation("Deleting incident {IncidentId} by user {UserId}", id, _currentUserService.UserId);

            var command = new DeleteIncidentCommand { Id = id };
            var result = await _mediator.Send(command);
            
            if (!result)
            {
                return NotFound(new { message = "Incident not found or access denied" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting incident {IncidentId}", id);
            return BadRequest(new { message = "An error occurred while deleting the incident" });
        }
    }

    /// <summary>
    /// Get incident statistics for dashboard
    /// </summary>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public async Task<ActionResult<object>> GetIncidentStatistics()
    {
        try
        {
            // TODO: Implement incident statistics query
            _logger.LogInformation("Getting incident statistics");

            // Return mock statistics for now
            var stats = new
            {
                TotalIncidents = 0,
                OpenIncidents = 0,
                ClosedIncidents = 0,
                CriticalIncidents = 0,
                IncidentsByMonth = new object[] { }
            };

            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving incident statistics");
            return BadRequest(new { message = "An error occurred while retrieving statistics" });
        }
    }

    /// <summary>
    /// Upload attachments for an incident
    /// </summary>
    [HttpPost("{id}/attachments")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> UploadAttachments(int id, IFormFileCollection files)
    {
        try
        {
            _logger.LogInformation("Uploading {FileCount} attachments for incident {IncidentId}", files.Count, id);

            if (files == null || files.Count == 0)
            {
                return BadRequest(new { message = "No files provided" });
            }

            // Validate files
            const long maxFileSize = 50 * 1024 * 1024; // 50MB
            var allowedTypes = new[] { "image/", "video/", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument" };
            
            foreach (var file in files)
            {
                if (file.Length > maxFileSize)
                {
                    return BadRequest(new { message = $"File {file.FileName} exceeds maximum size of 50MB" });
                }
                
                if (!allowedTypes.Any(type => file.ContentType.StartsWith(type)))
                {
                    return BadRequest(new { message = $"File type {file.ContentType} is not allowed" });
                }
            }

            // TODO: Implement actual file storage and attachment creation
            // For now, simulate successful upload
            var attachmentIds = new List<int>();
            
            // This would typically:
            // 1. Store files to disk/cloud storage
            // 2. Create IncidentAttachment entities
            // 3. Link them to the incident
            // 4. Return the attachment IDs

            return Ok(new { attachmentIds, message = $"Successfully uploaded {files.Count} files" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading attachments for incident {IncidentId}", id);
            return BadRequest(new { message = "An error occurred while uploading files" });
        }
    }
}

/// <summary>
/// Request model for creating an incident
/// </summary>
public class CreateIncidentRequest
{
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public DateTime IncidentDate { get; set; }
    public string Location { get; set; } = string.Empty;
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
    public string? WitnessNames { get; set; }
    public string? ImmediateActionsTaken { get; set; }
}

/// <summary>
/// Request model for updating an incident
/// </summary>
public class UpdateIncidentRequest
{
    public string? Title { get; set; }
    public string? Description { get; set; }
    public string? Severity { get; set; }
    public string? Status { get; set; }
    public string? Location { get; set; }
}