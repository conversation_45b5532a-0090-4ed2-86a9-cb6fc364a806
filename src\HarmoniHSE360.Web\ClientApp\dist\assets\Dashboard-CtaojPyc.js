import{u as f,j as s,c as t,a as o,b as d,d as v}from"./index-BDmn1jd7.js";import{w as u,j as i,x as c,y as m,z as a,A as l,B as x,D as h,E as j,o as C}from"./coreui-vendor-CeiuoDvP.js";import{u as b}from"./react-vendor-Dc0cLFd6.js";import"./redux-vendor-Wiuug2_S.js";var r=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M183.505,496,86.237,398.73,229.412,255.556,86.237,112.38l97.268-97.27L326.732,158.338l11.316,11.209,85.9,85.9,0,0,.051.05-11.311,11.419-85.9,85.9-.055-.054Zm-52.013-97.27,52.013,52.014L326.629,307.62l.055.054L378.8,255.556l-52.127-52.128-11.308-11.2L183.505,60.366,131.492,112.38,274.667,255.556Z' class='ci-primary'/>"],w=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M48,464V144H16V472a24.027,24.027,0,0,0,24,24H368V464H48Z' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M144,400H112V80H80V408a24.027,24.027,0,0,0,24,24H432V400H144Z' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M472,16H168a24.027,24.027,0,0,0-24,24V344a24.027,24.027,0,0,0,24,24H472a24.027,24.027,0,0,0,24-24V40A24.027,24.027,0,0,0,472,16Zm-8,320H176V48H464Z' class='ci-primary'/><polygon fill='var(--ci-primary-color, currentColor)' points='304 288 336 288 336 204 420 204 420 172 336 172 336 88 304 88 304 172 220 172 220 204 304 204 304 288' class='ci-primary'/>"];const R=()=>{const{user:e}=f(),n=b(),g={totalIncidents:0,openIncidents:0,closedIncidents:0,criticalIncidents:0},p=null;return console.log("Dashboard component rendering",{user:e,stats:g}),e?s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("h1",{children:"Dashboard"}),s.jsxs("p",{className:"text-medium-emphasis",children:["Welcome back, ",e==null?void 0:e.name,"! Here's your HSE overview."]})]}),s.jsxs(u,{color:"info",className:"d-flex align-items-center",children:[s.jsx(i,{icon:t,className:"flex-shrink-0 me-2"}),s.jsxs("div",{children:[s.jsx("strong",{children:"Welcome to HarmoniHSE360!"})," This system manages health, safety, and environmental data for British School Jakarta.",s.jsxs(c,{color:"primary",size:"sm",className:"ms-3",onClick:()=>n("/incidents/create"),children:[s.jsx(i,{icon:w,size:"sm",className:"me-1"}),"Report Incident"]})]})]}),p,s.jsxs(m,{className:"mb-4",children:[s.jsx(a,{sm:6,lg:3,children:s.jsx(l,{className:"mb-4 dashboard-widget",color:"primary",value:s.jsxs(s.Fragment,{children:[0," ",s.jsx("span",{className:"fs-6 fw-normal",children:"incidents"})]}),title:"Total Incidents",action:s.jsx(i,{icon:o,height:36,className:"my-4 text-white"}),onClick:()=>n("/incidents"),style:{cursor:"pointer"}})}),s.jsx(a,{sm:6,lg:3,children:s.jsx(l,{className:"mb-4 dashboard-widget",color:"warning",value:s.jsxs(s.Fragment,{children:[0," ",s.jsx("span",{className:"fs-6 fw-normal",children:"open"})]}),title:"Open Incidents",action:s.jsx(i,{icon:d,height:36,className:"my-4 text-white"}),onClick:()=>n("/incidents?status=Reported,UnderInvestigation,AwaitingAction"),style:{cursor:"pointer"}})}),s.jsx(a,{sm:6,lg:3,children:s.jsx(l,{className:"mb-4 dashboard-widget",color:"danger",value:s.jsxs(s.Fragment,{children:[0," ",s.jsx("span",{className:"fs-6 fw-normal",children:"critical"})]}),title:"Critical Incidents",action:s.jsx(i,{icon:o,height:36,className:"my-4 text-white"}),onClick:()=>n("/incidents?severity=Critical"),style:{cursor:"pointer"}})}),s.jsx(a,{sm:6,lg:3,children:s.jsx(l,{className:"mb-4 dashboard-widget",color:"success",value:s.jsxs(s.Fragment,{children:[0," ",s.jsx("span",{className:"fs-6 fw-normal",children:"resolved"})]}),title:"Resolved Incidents",action:s.jsx(i,{icon:t,height:36,className:"my-4 text-white"}),onClick:()=>n("/incidents?status=Resolved,Closed"),style:{cursor:"pointer"}})})]}),s.jsxs(m,{children:[s.jsx(a,{md:6,children:s.jsxs(x,{className:"mb-4",children:[s.jsx(h,{className:"d-flex justify-content-between align-items-center",children:s.jsx("strong",{children:"Quick Actions"})}),s.jsx(j,{children:s.jsxs("div",{className:"d-grid gap-3",children:[s.jsxs(c,{color:"primary",className:"d-flex align-items-center justify-content-between",onClick:()=>n("/incidents/create"),children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx(i,{icon:o,className:"me-2"}),"Report New Incident"]}),s.jsx(i,{icon:r,size:"sm"})]}),s.jsxs(c,{color:"secondary",variant:"outline",className:"d-flex align-items-center justify-content-between",onClick:()=>n("/incidents"),children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx(i,{icon:d,className:"me-2"}),"View All Incidents"]}),s.jsx(i,{icon:r,size:"sm"})]}),s.jsxs(c,{color:"secondary",variant:"outline",className:"d-flex align-items-center justify-content-between",onClick:()=>n("/incidents/my-reports"),children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx(i,{icon:v,className:"me-2"}),"My Incident Reports"]}),s.jsx(i,{icon:r,size:"sm"})]}),s.jsxs(c,{color:"success",variant:"outline",className:"d-flex align-items-center justify-content-between",disabled:!0,children:[s.jsxs("div",{className:"d-flex align-items-center",children:[s.jsx(i,{icon:t,className:"me-2"}),"Risk Assessment (Coming Soon)"]}),s.jsx(i,{icon:r,size:"sm"})]})]})})]})}),s.jsx(a,{md:6,children:s.jsxs(x,{className:"mb-4",children:[s.jsx(h,{children:s.jsx("strong",{children:"Your Profile"})}),s.jsxs(j,{children:[s.jsxs("div",{className:"mb-3",children:[s.jsx("strong",{children:"Name:"})," ",e==null?void 0:e.name]}),s.jsxs("div",{className:"mb-3",children:[s.jsx("strong",{children:"Employee ID:"})," ",e==null?void 0:e.employeeId]}),s.jsxs("div",{className:"mb-3",children:[s.jsx("strong",{children:"Department:"})," ",e==null?void 0:e.department]}),s.jsxs("div",{className:"mb-3",children:[s.jsx("strong",{children:"Position:"})," ",e==null?void 0:e.position]}),s.jsxs("div",{className:"mb-3",children:[s.jsx("strong",{children:"Roles:"})," ",e==null?void 0:e.roles.map((N,y)=>s.jsx(C,{color:"primary",className:"me-1",children:N},y))]})]})]})})]})]}):(console.log("No user found in Dashboard"),s.jsx("div",{children:"Loading user..."}))};export{R as default};
