import{j as e,d as w,o as C,a as v,b as M,c as G}from"./index-BDmn1jd7.js";import{u as J,r as n}from"./react-vendor-Dc0cLFd6.js";import{v as V,y as L,z as m,B as Q,D as X,x as y,j as i,E as Y,w as _,H as q,J as K,Q as k,R as O,S as ee,T as z,U as r,V as se,W as a,m as te,n as ie,p as ne,r as S,X as re,Y as b,o as P}from"./coreui-vendor-CeiuoDvP.js";import"./redux-vendor-Wiuug2_S.js";const de=()=>{const h=J(),[E,B]=n.useState([]),[U,I]=n.useState(!0),[A,N]=n.useState(null),[o,T]=n.useState(""),[x,R]=n.useState(""),[u,D]=n.useState(""),[l,j]=n.useState(1),[p]=n.useState(10);console.log("IncidentList component rendering"),n.useEffect(()=>{(async()=>{try{I(!0),await new Promise(d=>setTimeout(d,1e3)),B([{id:1,title:"Student injured in Chemistry Lab",description:"Student suffered minor burns during experiment",severity:"Moderate",status:"UnderInvestigation",incidentDate:"2025-06-02T14:30:00Z",location:"Chemistry Lab - Room 205",reporterName:"Dr. Sarah Johnson",createdAt:"2025-06-02T14:35:00Z"},{id:2,title:"Slip and fall in hallway",description:"Wet floor caused student to slip near entrance",severity:"Minor",status:"Resolved",incidentDate:"2025-06-01T09:15:00Z",location:"Main Building - Ground Floor",reporterName:"Mr. David Wilson",createdAt:"2025-06-01T09:20:00Z"},{id:3,title:"Fire alarm system malfunction",description:"False alarm triggered evacuating entire building",severity:"Serious",status:"AwaitingAction",incidentDate:"2025-05-30T11:45:00Z",location:"East Wing - 3rd Floor",reporterName:"Ms. Emily Chen",createdAt:"2025-05-30T11:50:00Z"}]),N(null)}catch(t){N("Failed to load incidents. Please try again."),console.error("Error loading incidents:",t)}finally{I(!1)}})()},[]);const Z=s=>{const t={Minor:"success",Moderate:"warning",Serious:"danger",Critical:"dark"};return e.jsx(P,{color:t[s],children:s})},H=s=>{const t={Reported:"info",UnderInvestigation:"warning",AwaitingAction:"danger",Resolved:"success",Closed:"secondary"},d={Reported:w,UnderInvestigation:v,AwaitingAction:v,Resolved:M,Closed:G};return e.jsxs(P,{color:t[s],children:[e.jsx(i,{icon:d[s],size:"sm",className:"me-1"}),s.replace(/([A-Z])/g," $1").trim()]})},F=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),c=E.filter(s=>{const t=s.title.toLowerCase().includes(o.toLowerCase())||s.description.toLowerCase().includes(o.toLowerCase())||s.location.toLowerCase().includes(o.toLowerCase()),d=!x||s.status===x,$=!u||s.severity===u;return t&&d&&$}),f=Math.ceil(c.length/p),g=(l-1)*p,W=c.slice(g,g+p);return U?e.jsxs("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:[e.jsx(V,{size:"sm",className:"text-primary"}),e.jsx("span",{className:"ms-2",children:"Loading incidents..."})]}):e.jsx(L,{children:e.jsx(m,{xs:12,children:e.jsxs(Q,{className:"shadow-sm",children:[e.jsxs(X,{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-0",style:{color:"var(--harmoni-charcoal)",fontFamily:"Poppins, sans-serif"},children:"Incident Reports"}),e.jsx("small",{className:"text-muted",children:"Manage and track all incident reports"})]}),e.jsxs(y,{color:"primary",onClick:()=>h("/incidents/create"),className:"d-flex align-items-center",children:[e.jsx(i,{icon:w,size:"sm",className:"me-2"}),"Report Incident"]})]}),e.jsxs(Y,{children:[A&&e.jsx(_,{color:"danger",dismissible:!0,onClose:()=>N(null),children:A}),e.jsxs(L,{className:"mb-4",children:[e.jsx(m,{md:4,children:e.jsxs(q,{children:[e.jsx(K,{placeholder:"Search incidents...",value:o,onChange:s=>T(s.target.value)}),e.jsx(y,{type:"button",color:"primary",variant:"outline",children:e.jsx(i,{icon:C})})]})}),e.jsx(m,{md:3,children:e.jsxs(k,{value:x,onChange:s=>R(s.target.value),children:[e.jsx("option",{value:"",children:"All Statuses"}),e.jsx("option",{value:"Reported",children:"Reported"}),e.jsx("option",{value:"UnderInvestigation",children:"Under Investigation"}),e.jsx("option",{value:"AwaitingAction",children:"Awaiting Action"}),e.jsx("option",{value:"Resolved",children:"Resolved"}),e.jsx("option",{value:"Closed",children:"Closed"})]})}),e.jsx(m,{md:3,children:e.jsxs(k,{value:u,onChange:s=>D(s.target.value),children:[e.jsx("option",{value:"",children:"All Severities"}),e.jsx("option",{value:"Minor",children:"Minor"}),e.jsx("option",{value:"Moderate",children:"Moderate"}),e.jsx("option",{value:"Serious",children:"Serious"}),e.jsx("option",{value:"Critical",children:"Critical"})]})}),e.jsx(m,{md:2,children:e.jsxs(y,{color:"secondary",variant:"outline",className:"w-100",onClick:()=>{T(""),R(""),D(""),j(1)},children:[e.jsx(i,{icon:C,size:"sm",className:"me-2"}),"Clear"]})})]}),c.length===0?e.jsxs("div",{className:"text-center py-5",children:[e.jsx(i,{icon:v,className:"text-muted mb-3",style:{fontSize:"3rem"}}),e.jsx("h5",{className:"text-muted",children:"No incidents found"}),e.jsx("p",{className:"text-muted",children:o||x||u?"Try adjusting your filters or search criteria.":"No incidents have been reported yet."}),e.jsxs(y,{color:"primary",onClick:()=>h("/incidents/create"),className:"mt-3",children:[e.jsx(i,{icon:w,size:"sm",className:"me-2"}),"Report First Incident"]})]}):e.jsxs(e.Fragment,{children:[e.jsxs(O,{responsive:!0,hover:!0,children:[e.jsx(ee,{children:e.jsxs(z,{children:[e.jsx(r,{scope:"col",children:"Title"}),e.jsx(r,{scope:"col",children:"Severity"}),e.jsx(r,{scope:"col",children:"Status"}),e.jsx(r,{scope:"col",children:"Location"}),e.jsx(r,{scope:"col",children:"Reporter"}),e.jsx(r,{scope:"col",children:"Date"}),e.jsx(r,{scope:"col",children:"Actions"})]})}),e.jsx(se,{children:W.map(s=>e.jsxs(z,{children:[e.jsx(a,{children:e.jsxs("div",{children:[e.jsx("strong",{children:s.title}),e.jsx("br",{}),e.jsx("small",{className:"text-muted",children:s.description.length>50?`${s.description.substring(0,50)}...`:s.description})]})}),e.jsx(a,{children:Z(s.severity)}),e.jsx(a,{children:H(s.status)}),e.jsx(a,{children:s.location}),e.jsx(a,{children:s.reporterName}),e.jsx(a,{children:e.jsxs("div",{children:[e.jsx("small",{children:F(s.incidentDate)}),e.jsx("br",{}),e.jsxs("small",{className:"text-muted",children:["Reported: ",F(s.createdAt)]})]})}),e.jsx(a,{children:e.jsxs(te,{children:[e.jsx(ie,{color:"light",size:"sm",caret:!1,children:e.jsx(i,{icon:M})}),e.jsxs(ne,{children:[e.jsxs(S,{onClick:()=>h(`/incidents/${s.id}`),children:[e.jsx(i,{icon:C,size:"sm",className:"me-2"}),"View Details"]}),e.jsxs(S,{onClick:()=>h(`/incidents/${s.id}/edit`),children:[e.jsx(i,{icon:C,size:"sm",className:"me-2"}),"Edit"]}),e.jsx(S,{style:{borderTop:"1px solid #dee2e6"}}),e.jsxs(S,{className:"text-danger",children:[e.jsx(i,{icon:v,size:"sm",className:"me-2"}),"Delete"]})]})]})})]},s.id))})]}),f>1&&e.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-4",children:[e.jsxs("div",{className:"text-muted",children:["Showing ",g+1," to ",Math.min(g+p,c.length)," of ",c.length," incidents"]}),e.jsxs(re,{"aria-label":"Incidents pagination",children:[e.jsx(b,{disabled:l===1,onClick:()=>j(l-1),children:"Previous"}),[...Array(f)].map((s,t)=>e.jsx(b,{active:l===t+1,onClick:()=>j(t+1),children:t+1},t+1)),e.jsx(b,{disabled:l===f,onClick:()=>j(l+1),children:"Next"})]})]})]})]})]})})})};export{de as default};
