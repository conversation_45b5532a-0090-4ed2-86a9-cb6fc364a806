{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npx create-react-app:*)", "Bash(npx @coreui/coreui-react-app@latest:*)", "Bash(dotnet build)", "Bash(dotnet build:*)", "Bash(find:*)", "Bash(sudo rm:*)", "Bash(rm:*)", "Bash(npm install)", "Bash(npm start)", "Bash(ls:*)", "Bash(npm uninstall:*)", "Bash(npm install:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout 30s npm start)", "<PERSON><PERSON>(timeout 45s npm start)", "Bash(GENERATE_SOURCEMAP=false npm start)", "Bash(dotnet run:*)", "Bash(grep:*)", "Bash(export NODE_OPTIONS=\"--max_old_space_size=4096\")", "<PERSON><PERSON>(curl:*)", "Bash(dotnet clean:*)", "Bash(dotnet ef migrations add:*)", "Bash(dotnet ef database update:*)", "Bash(dotnet ef migrations:*)", "Bash(npm ls:*)", "Bash(npm run build:*)", "Bash(node:*)", "<PERSON><PERSON>(docker-compose ps:*)", "<PERSON><PERSON>(hostname:*)", "Bash(dotnet dev-certs:*)", "Bash(ss:*)", "Bash(cp:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(killall:*)", "Bash(dotnet new:*)", "Bash(dotnet sln add:*)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker-compose down:*)", "Bash(docker volume rm:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:antblazor.com)", "Bash(dotnet restore)", "Bash(rg:*)", "<PERSON><PERSON>(dotnet nuget locals:*)", "Bash(dotnet restore:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(docker compose:*)", "Bash(dotnet add src/HarmoniHSE360.Application reference src/HarmoniHSE360.Domain)", "Bash(dotnet add:*)", "Bash(npm create:*)", "Bash(npx create-vite:*)", "Bash(npm init:*)", "Bash(dotnet sln remove:*)", "<PERSON><PERSON>(docker-compose restart:*)", "<PERSON><PERSON>(docker-compose exec:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:coreui.io)", "<PERSON><PERSON>(docker inspect:*)", "Bash(dotnet:*)", "Bash(npm cache clean:*)", "Bash(npm run dev:*)", "Bash(PGPASSWORD=postgres123 psql -h localhost -U postgres -d HarmoniHSE360_Dev -c \"SELECT COUNT(*) FROM \\\"Incidents\\\";\")"]}, "enableAllProjectMcpServers": false}