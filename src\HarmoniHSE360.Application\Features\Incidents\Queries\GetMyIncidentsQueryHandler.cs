using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using HarmoniHSE360.Application.Common.Interfaces;
using HarmoniHSE360.Application.Features.Incidents.DTOs;
using HarmoniHSE360.Domain.Entities;

namespace HarmoniHSE360.Application.Features.Incidents.Queries;

public class GetMyIncidentsQueryHandler : IRequestHandler<GetMyIncidentsQuery, GetIncidentsResponse>
{
    private readonly IApplicationDbContext _context;
    private readonly IMemoryCache _cache;
    private const string MY_INCIDENTS_CACHE_KEY_PREFIX = "my_incidents_";
    private const int CACHE_DURATION_MINUTES = 5;

    public GetMyIncidentsQueryHandler(IApplicationDbContext context, IMemoryCache cache)
    {
        _context = context;
        _cache = cache;
    }

    public async Task<GetIncidentsResponse> Handle(GetMyIncidentsQuery request, CancellationToken cancellationToken)
    {
        // Generate cache key based on query parameters
        var cacheKey = $"{MY_INCIDENTS_CACHE_KEY_PREFIX}{request.UserId}_{request.PageNumber}_{request.PageSize}_{request.Status}_{request.Severity}_{request.SearchTerm}";

        // Try to get from cache first
        if (_cache.TryGetValue<GetIncidentsResponse>(cacheKey, out var cachedResponse))
        {
            return cachedResponse!;
        }

        var query = _context.Incidents
            .Include(i => i.Attachments)
            .Include(i => i.InvolvedPersons)
            .Include(i => i.CorrectiveActions)
            .Where(i => i.ReporterId == request.UserId)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrWhiteSpace(request.SearchTerm))
        {
            query = query.Where(i => 
                i.Title.Contains(request.SearchTerm) ||
                i.Description.Contains(request.SearchTerm) ||
                i.Location.Contains(request.SearchTerm));
        }

        if (!string.IsNullOrWhiteSpace(request.Status) && Enum.TryParse<IncidentStatus>(request.Status, out var status))
        {
            query = query.Where(i => i.Status == status);
        }

        if (!string.IsNullOrWhiteSpace(request.Severity) && Enum.TryParse<IncidentSeverity>(request.Severity, out var severity))
        {
            query = query.Where(i => i.Severity == severity);
        }

        // Get total count before pagination
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply pagination and ordering
        var incidents = await query
            .OrderByDescending(i => i.IncidentDate)
            .ThenByDescending(i => i.CreatedAt)
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        // Map to DTOs
        var incidentDtos = incidents.Select(i => new IncidentDto
        {
            Id = i.Id,
            Title = i.Title,
            Description = i.Description,
            Severity = i.Severity.ToString(),
            Status = i.Status.ToString(),
            IncidentDate = i.IncidentDate,
            Location = i.Location,
            ReporterName = i.ReporterName,
            ReporterEmail = i.ReporterEmail,
            ReporterDepartment = i.ReporterDepartment,
            InjuryType = i.InjuryType?.ToString(),
            MedicalTreatmentProvided = i.MedicalTreatmentProvided,
            EmergencyServicesContacted = i.EmergencyServicesContacted,
            WitnessNames = i.WitnessNames,
            ImmediateActionsTaken = i.ImmediateActionsTaken,
            AttachmentsCount = i.Attachments.Count,
            InvolvedPersonsCount = i.InvolvedPersons.Count,
            CorrectiveActionsCount = i.CorrectiveActions.Count,
            CreatedAt = i.CreatedAt,
            CreatedBy = i.CreatedBy,
            LastModifiedAt = i.LastModifiedAt,
            LastModifiedBy = i.LastModifiedBy
        }).ToList();

        var response = new GetIncidentsResponse
        {
            Incidents = incidentDtos,
            TotalCount = totalCount,
            PageNumber = request.PageNumber,
            PageSize = request.PageSize
        };

        // Cache the response
        var cacheOptions = new MemoryCacheEntryOptions()
            .SetAbsoluteExpiration(TimeSpan.FromMinutes(CACHE_DURATION_MINUTES))
            .SetSlidingExpiration(TimeSpan.FromMinutes(2));

        _cache.Set(cacheKey, response, cacheOptions);

        return response;
    }
}