import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  CCard,
  CCardBody,
  CCardHeader,
  CCol,
  CRow,
  CButton,
  CSpinner,
  CAlert,
  CBadge,
  CListGroup,
  CListGroupItem,
  CCallout,
} from '@coreui/react';
import CIcon from '@coreui/icons-react';
import {
  cilWarning,
  cilTask,
  cilShieldAlt,
  cilClipboard,
  cilUser,
} from '@coreui/icons';
import { Icon } from '../../components/common/Icon';
import { 
  faArrowLeft,
  faPen,
  faTrash,
  faMapMarkerAlt,
  faCalendar,
} from '@fortawesome/free-solid-svg-icons';
import { useGetIncidentQuery, useDeleteIncidentMutation } from '../../features/incidents/incidentApi';

const IncidentDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { data: incident, error, isLoading } = useGetIncidentQuery(Number(id));
  const [deleteIncident, { isLoading: isDeleting }] = useDeleteIncidentMutation();

  // Helper functions
  const getSeverityBadge = (severity: string) => {
    const variants = {
      Minor: 'success',
      Moderate: 'warning',
      Serious: 'danger',
      Critical: 'dark'
    };
    return <CBadge color={variants[severity as keyof typeof variants]}>{severity}</CBadge>;
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      Reported: 'info',
      UnderInvestigation: 'warning',
      AwaitingAction: 'danger',
      Resolved: 'success',
      Closed: 'secondary'
    };
    const icons = {
      Reported: cilClipboard,
      UnderInvestigation: cilWarning,
      AwaitingAction: cilWarning,
      Resolved: cilTask,
      Closed: cilShieldAlt
    };
    
    return (
      <CBadge color={variants[status as keyof typeof variants]}>
        <CIcon icon={icons[status as keyof typeof icons]} size="sm" className="me-1" />
        {status.replace(/([A-Z])/g, ' $1').trim()}
      </CBadge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <CSpinner size="sm" className="text-primary" />
        <span className="ms-2">Loading incident details...</span>
      </div>
    );
  }

  if (error || !incident) {
    return (
      <CAlert color="danger">
        Failed to load incident details. Please try again.
        <div className="mt-3">
          <CButton color="primary" onClick={() => navigate('/incidents')}>
            <Icon icon={faArrowLeft} className="me-2" />
            Back to List
          </CButton>
        </div>
      </CAlert>
    );
  }

  return (
    <CRow>
      <CCol xs={12}>
        <CCard className="shadow-sm">
          <CCardHeader className="d-flex justify-content-between align-items-center">
            <div>
              <h4 className="mb-0">Incident Details</h4>
              <small className="text-muted">ID: {incident.id}</small>
            </div>
            <div>
              <CButton
                color="light"
                className="me-2"
                onClick={() => navigate('/incidents')}
              >
                <Icon icon={faArrowLeft} size="sm" className="me-2" />
                Back
              </CButton>
              <CButton
                color="primary"
                className="me-2"
                onClick={() => navigate(`/incidents/${id}/edit`)}
              >
                <Icon icon={faPen} size="sm" className="me-2" />
                Edit
              </CButton>
              <CButton
                color="danger"
                disabled={isDeleting}
                onClick={async () => {
                  if (window.confirm('Are you sure you want to delete this incident? This action cannot be undone.')) {
                    try {
                      await deleteIncident(Number(id)).unwrap();
                      navigate('/incidents');
                    } catch (error) {
                      console.error('Failed to delete incident:', error);
                      alert('Failed to delete incident. Please try again.');
                    }
                  }
                }}
              >
                {isDeleting ? (
                  <>
                    <CSpinner size="sm" className="me-2" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Icon icon={faTrash} size="sm" className="me-2" />
                    Delete
                  </>
                )}
              </CButton>
            </div>
          </CCardHeader>
          
          <CCardBody>
            <CRow>
              <CCol md={8}>
                <h5 className="mb-3">{incident.title}</h5>
                
                <div className="mb-4">
                  <h6 className="text-muted">Description</h6>
                  <p>{incident.description}</p>
                </div>

                <CRow className="mb-4">
                  <CCol sm={6}>
                    <h6 className="text-muted">Status</h6>
                    <p>{getStatusBadge(incident.status)}</p>
                  </CCol>
                  <CCol sm={6}>
                    <h6 className="text-muted">Severity</h6>
                    <p>{getSeverityBadge(incident.severity)}</p>
                  </CCol>
                </CRow>

                <CRow className="mb-4">
                  <CCol sm={6}>
                    <h6 className="text-muted">
                      <Icon icon={faCalendar} size="sm" className="me-1" />
                      Incident Date
                    </h6>
                    <p>{formatDate(incident.incidentDate)}</p>
                  </CCol>
                  <CCol sm={6}>
                    <h6 className="text-muted">
                      <Icon icon={faMapMarkerAlt} size="sm" className="me-1" />
                      Location
                    </h6>
                    <p>{incident.location}</p>
                  </CCol>
                </CRow>

                {incident.injuryType && (
                  <div className="mb-4">
                    <h6 className="text-muted">Injury Details</h6>
                    <CListGroup>
                      <CListGroupItem>
                        <strong>Injury Type:</strong> {incident.injuryType}
                      </CListGroupItem>
                      <CListGroupItem>
                        <strong>Medical Treatment Provided:</strong> {incident.medicalTreatmentProvided ? 'Yes' : 'No'}
                      </CListGroupItem>
                      <CListGroupItem>
                        <strong>Emergency Services Contacted:</strong> {incident.emergencyServicesContacted ? 'Yes' : 'No'}
                      </CListGroupItem>
                    </CListGroup>
                  </div>
                )}

                {incident.witnessNames && (
                  <div className="mb-4">
                    <h6 className="text-muted">Witness Information</h6>
                    <p>{incident.witnessNames}</p>
                  </div>
                )}

                {incident.immediateActionsTaken && (
                  <div className="mb-4">
                    <h6 className="text-muted">Immediate Actions Taken</h6>
                    <p>{incident.immediateActionsTaken}</p>
                  </div>
                )}
              </CCol>

              <CCol md={4}>
                <div className="border-start ps-4">
                  <h6 className="text-muted mb-3">Reporter Information</h6>
                  <div className="mb-3">
                    <CIcon icon={cilUser} size="sm" className="me-2" />
                    <strong>{incident.reporterName}</strong>
                  </div>
                  <div className="mb-3">
                    <small className="text-muted">Email: {incident.reporterEmail}</small>
                  </div>
                  <div className="mb-4">
                    <small className="text-muted">Department: {incident.reporterDepartment}</small>
                  </div>

                  <h6 className="text-muted mb-3">Related Information</h6>
                  <CListGroup className="mb-4">
                    <CListGroupItem className="d-flex justify-content-between">
                      <div>
                        <span>Attachments</span>
                        <small className="d-block text-muted">Photos, videos, documents</small>
                      </div>
                      <CBadge color="info">{incident.attachmentsCount || 0}</CBadge>
                    </CListGroupItem>
                    <CListGroupItem className="d-flex justify-content-between">
                      <div>
                        <span>Involved Persons</span>
                        <small className="d-block text-muted">Formally linked individuals</small>
                      </div>
                      <CBadge color="info">{incident.involvedPersonsCount || 0}</CBadge>
                    </CListGroupItem>
                    <CListGroupItem className="d-flex justify-content-between">
                      <div>
                        <span>Corrective Actions</span>
                        <small className="d-block text-muted">Assigned CAPA tasks</small>
                      </div>
                      <CBadge color="info">{incident.correctiveActionsCount || 0}</CBadge>
                    </CListGroupItem>
                  </CListGroup>

                  {(incident.attachmentsCount === 0 && incident.involvedPersonsCount === 0 && incident.correctiveActionsCount === 0) && (
                    <CCallout color="info" className="small mb-4">
                      <strong>Note:</strong> File uploads, formal person assignments, and corrective actions are typically added during the investigation phase.
                      <div className="mt-2">
                        <CButton 
                          color="primary" 
                          size="sm" 
                          variant="outline"
                          onClick={() => {/* TODO: Open involved persons modal */}}
                        >
                          Add Involved Persons
                        </CButton>
                      </div>
                    </CCallout>
                  )}

                  <h6 className="text-muted mb-3">Audit Information</h6>
                  <div className="small text-muted">
                    <p className="mb-1">
                      <strong>Created:</strong> {formatDate(incident.createdAt)}
                    </p>
                    {incident.createdBy && (
                      <p className="mb-1">
                        <strong>Created By:</strong> {incident.createdBy}
                      </p>
                    )}
                    {incident.lastModifiedAt && (
                      <>
                        <p className="mb-1">
                          <strong>Modified:</strong> {formatDate(incident.lastModifiedAt)}
                        </p>
                        {incident.lastModifiedBy && (
                          <p className="mb-1">
                            <strong>Modified By:</strong> {incident.lastModifiedBy}
                          </p>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
      </CCol>
    </CRow>
  );
};

export default IncidentDetail;