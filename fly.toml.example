# Fly.io configuration file for HarmoniHSE360
# Copy this file to fly.toml and customize as needed

app = "harmonihse360-app"
primary_region = "sjc"

[build]
  dockerfile = "Dockerfile.flyio"

[env]
  ASPNETCORE_ENVIRONMENT = "Production"
  ASPNETCORE_URLS = "http://+:8080"

[[services]]
  internal_port = 8080
  protocol = "tcp"

  [[services.ports]]
    handlers = ["http"]
    port = 80
    force_https = true

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

  [services.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

[[services.http_checks]]
  interval = "10s"
  grace_period = "5s"
  method = "GET"
  path = "/health"
  protocol = "http"
  timeout = "2s"
  tls_skip_verify = false

[mounts]
  source = "harmonihse360_uploads"
  destination = "/app/uploads"

# Optional: Machine configuration for specific resource requirements
# [vm]
#   cpu_kind = "shared"
#   cpus = 1
#   memory_mb = 512
