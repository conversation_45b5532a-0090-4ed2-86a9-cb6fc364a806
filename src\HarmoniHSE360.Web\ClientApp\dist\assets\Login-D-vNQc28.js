import{e as R,f as U,s as z,g as V,h as B,i as N,j as e,k as G,l as q,m as W,n as Z}from"./index-BDmn1jd7.js";import{u as _,c as K,r as h}from"./react-vendor-Dc0cLFd6.js";import{u as T,c as $,o as J,a as O,b as v}from"./index.esm-irDu7M73.js";import{h as Q,y as x,z as c,F as X,B as w,E as S,G as Y,w as ee,j as p,H as k,I as E,J as F,K as se,x as j,v as ae,L as re,M as ie,N as le,O as ne,P as te}from"./coreui-vendor-CeiuoDvP.js";import"./redux-vendor-Wiuug2_S.js";var ce=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M384,200V144a128,128,0,0,0-256,0v56H88V328c0,92.635,75.364,168,168,168s168-75.365,168-168V200ZM160,144a96,96,0,0,1,192,0v56H160ZM392,328c0,74.99-61.01,136-136,136s-136-61.01-136-136V232H392Z' class='ci-primary'/>"];const oe=O({email:v().required("Email is required").email("Please enter a valid email address"),password:v().required("Password is required").min(6,"Password must be at least 6 characters")}),je=()=>{var y,C;const i=R(),o=_(),H=K(),l=U(z),[L,{isLoading:n}]=V(),{data:u}=B(),[g,f]=h.useState(!1),d=((C=(y=H.state)==null?void 0:y.from)==null?void 0:C.pathname)||"/dashboard",{register:m,handleSubmit:A,formState:{errors:r},setValue:b}=T({resolver:J(oe),defaultValues:{email:"",password:"",rememberMe:!1}});h.useEffect(()=>{l.isAuthenticated&&o(d,{replace:!0})},[l.isAuthenticated,o,d]),h.useEffect(()=>{l.error&&i(N())},[]);const P=async s=>{var a;try{i(q());const t=await L(s).unwrap();i(W(t)),o(d,{replace:!0})}catch(t){const M=((a=t==null?void 0:t.data)==null?void 0:a.message)||"Login failed. Please try again.";i(Z(M))}},D=(s,a)=>{b("email",s),b("password",a),f(!1)},I=()=>{i(N())};return e.jsx("div",{className:"bg-light min-vh-100 d-flex flex-row align-items-center",style:{backgroundColor:"var(--harmoni-grey)"},children:e.jsx(Q,{children:e.jsx(x,{className:"justify-content-center",children:e.jsxs(c,{md:8,lg:6,children:[e.jsxs(X,{children:[e.jsx(w,{className:"p-4 shadow-lg",style:{borderRadius:"12px"},children:e.jsx(S,{children:e.jsxs(Y,{onSubmit:A(P),children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("div",{className:"mb-3",children:e.jsx("img",{src:"/Harmoni_HSE_360_Logo.png",alt:"HarmoniHSE360 Logo",style:{width:"120px",height:"auto",maxHeight:"80px"}})}),e.jsx("p",{className:"text-medium-emphasis",style:{fontSize:"14px",marginTop:"16px"},children:"Complete Safety. Seamless Harmony."})]}),e.jsx("h3",{className:"mb-3",style:{color:"var(--harmoni-charcoal)",fontFamily:"Poppins, sans-serif"},children:"Welcome Back"}),e.jsx("p",{className:"text-medium-emphasis mb-4",children:"Sign in to your account to continue"}),l.error&&e.jsxs(ee,{color:"danger",dismissible:!0,onClose:I,className:"d-flex align-items-center",children:[e.jsx(p,{icon:$,className:"flex-shrink-0 me-2"}),e.jsx("div",{children:l.error})]}),e.jsxs(k,{className:"mb-3",children:[e.jsx(E,{children:e.jsx(p,{icon:G})}),e.jsx(F,{...m("email"),type:"email",placeholder:"Email",autoComplete:"username",invalid:!!r.email,disabled:n})]}),r.email&&e.jsx("div",{className:"text-danger small mb-2",children:r.email.message}),e.jsxs(k,{className:"mb-4",children:[e.jsx(E,{children:e.jsx(p,{icon:ce})}),e.jsx(F,{...m("password"),type:"password",placeholder:"Password",autoComplete:"current-password",invalid:!!r.password,disabled:n})]}),r.password&&e.jsx("div",{className:"text-danger small mb-3",children:r.password.message}),e.jsx(se,{...m("rememberMe"),id:"rememberMe",label:"Remember me",className:"mb-4",disabled:n}),e.jsxs(x,{children:[e.jsx(c,{xs:6,children:e.jsx(j,{className:"px-4 py-2",type:"submit",disabled:n,style:{backgroundColor:"var(--harmoni-teal)",borderColor:"var(--harmoni-teal)",borderRadius:"8px",fontWeight:"500",fontSize:"16px",color:"white"},children:n?e.jsxs(e.Fragment,{children:[e.jsx(ae,{size:"sm",className:"me-2"}),"Signing in..."]}):"Sign In"})}),e.jsx(c,{xs:6,className:"text-right",children:e.jsx(j,{className:"px-0",onClick:()=>f(!g),style:{color:"var(--harmoni-teal)",textDecoration:"none",background:"none",border:"none",fontSize:"14px"},children:"Demo Users"})})]})]})})}),e.jsx(w,{className:"text-white py-5",style:{width:"44%",background:"linear-gradient(135deg, var(--harmoni-teal) 0%, var(--harmoni-blue) 100%)",borderRadius:"12px"},children:e.jsx(S,{className:"text-center",children:e.jsxs("div",{children:[e.jsx("h2",{style:{fontFamily:"Poppins, sans-serif",fontWeight:"600"},children:"Welcome to HarmoniHSE360"}),e.jsx("p",{className:"mb-4",style:{fontSize:"16px",opacity:"0.9"},children:"360° Coverage for a Safer School Environment"}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("div",{className:"feature-highlight mb-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-center mb-2",children:[e.jsx("div",{className:"rounded-circle d-flex align-items-center justify-content-center me-3",style:{width:"40px",height:"40px",backgroundColor:"rgba(255,255,255,0.2)"},children:"🛡️"}),e.jsx("h5",{className:"mb-0",style:{fontFamily:"Poppins, sans-serif"},children:"Safety First"})]}),e.jsx("p",{className:"small",style:{opacity:"0.8"},children:"Complete incident reporting and management"})]}),e.jsxs("div",{className:"feature-highlight mb-4",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-center mb-2",children:[e.jsx("div",{className:"rounded-circle d-flex align-items-center justify-content-center me-3",style:{width:"40px",height:"40px",backgroundColor:"rgba(255,255,255,0.2)"},children:"📊"}),e.jsx("h5",{className:"mb-0",style:{fontFamily:"Poppins, sans-serif"},children:"Real-time Analytics"})]}),e.jsx("p",{className:"small",style:{opacity:"0.8"},children:"Live dashboards and reporting"})]}),e.jsxs("div",{className:"feature-highlight",children:[e.jsxs("div",{className:"d-flex align-items-center justify-content-center mb-2",children:[e.jsx("div",{className:"rounded-circle d-flex align-items-center justify-content-center me-3",style:{width:"40px",height:"40px",backgroundColor:"rgba(255,255,255,0.2)"},children:"🌿"}),e.jsx("h5",{className:"mb-0",style:{fontFamily:"Poppins, sans-serif"},children:"Environmental Care"})]}),e.jsx("p",{className:"small",style:{opacity:"0.8"},children:"Sustainable practices tracking"})]})]})]})})})]}),g&&u&&e.jsx(x,{className:"mt-4",children:e.jsx(c,{children:e.jsxs(re,{color:"info",children:[e.jsx("h5",{children:"Demo User Accounts"}),e.jsx("p",{children:"Click on any user below to auto-fill login credentials:"}),e.jsx(ie,{children:u.users.map((s,a)=>e.jsxs(le,{itemKey:a,children:[e.jsxs(ne,{children:[e.jsx("strong",{children:s.name})," - ",s.role]}),e.jsx(te,{children:e.jsxs("div",{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{children:[e.jsxs("p",{className:"mb-1",children:[e.jsx("strong",{children:"Email:"})," ",s.email]}),e.jsxs("p",{className:"mb-1",children:[e.jsx("strong",{children:"Password:"})," ",s.password]}),e.jsxs("p",{className:"mb-0",children:[e.jsx("strong",{children:"Role:"})," ",s.role]})]}),e.jsx(j,{color:"primary",size:"sm",onClick:()=>D(s.email,s.password),children:"Use Credentials"})]})})]},a))})]})})})]})})})})};export{je as default};
