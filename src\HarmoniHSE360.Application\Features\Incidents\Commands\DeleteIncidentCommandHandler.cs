using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using HarmoniHSE360.Application.Common.Interfaces;

namespace HarmoniHSE360.Application.Features.Incidents.Commands;

public class DeleteIncidentCommandHandler : IRequestHandler<DeleteIncidentCommand, bool>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<DeleteIncidentCommandHandler> _logger;

    public DeleteIncidentCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IMemoryCache cache,
        ILogger<DeleteIncidentCommandHandler> logger)
    {
        _context = context;
        _currentUserService = currentUserService;
        _cache = cache;
        _logger = logger;
    }

    public async Task<bool> Handle(DeleteIncidentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Deleting incident {IncidentId} by user {UserEmail}", 
            request.Id, _currentUserService.Email);

        var incident = await _context.Incidents
            .Include(i => i.Attachments)
            .Include(i => i.InvolvedPersons)
            .Include(i => i.CorrectiveActions)
            .FirstOrDefaultAsync(i => i.Id == request.Id, cancellationToken);

        if (incident == null)
        {
            _logger.LogWarning("Incident {IncidentId} not found", request.Id);
            return false;
        }

        // TODO: Add authorization check here
        // For now, any authenticated user can delete any incident

        // Remove the incident (cascading delete will handle related entities)
        _context.Incidents.Remove(incident);
        await _context.SaveChangesAsync(cancellationToken);

        // Clear incident cache after successful deletion
        ClearIncidentCache();

        _logger.LogInformation("Incident {IncidentId} deleted successfully", request.Id);

        return true;
    }

    private void ClearIncidentCache()
    {
        try
        {
            // Use a simpler approach - remove common cache key patterns manually
            var commonKeys = new[]
            {
                "incidents_1_10___",
                "incidents_1_10__",
                "incidents_2_10___",
                "incidents_2_10__",
                "incidents_1_5___",
                "incidents_1_5__",
                "incidents_1_20___",
                "incidents_1_20__",
                "incidents_3_10___",
                "incidents_3_10__"
            };
            
            var removedCount = 0;
            foreach (var key in commonKeys)
            {
                _cache.Remove(key);
                removedCount++;
            }
            
            _logger.LogInformation("Cleared {Count} incident cache entries using pattern matching", removedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing incident cache");
        }
    }
}