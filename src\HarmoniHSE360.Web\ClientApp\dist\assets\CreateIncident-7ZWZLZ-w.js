import{j as e,a as E,b as _,p as ie,d as se,o as G}from"./index-BDmn1jd7.js";import{u as te,r as u}from"./react-vendor-Dc0cLFd6.js";import{u as ae,c as I,o as ne,a as re,b as o,d as O}from"./index.esm-irDu7M73.js";import{y as v,z as n,B as le,D as oe,j as a,o as V,v as F,x as j,E as ce,G as de,w as me,L as ue,M as he,N as g,O as y,P as b,Z as r,J as P,Q as k,_ as w,H as D,I as R}from"./coreui-vendor-CeiuoDvP.js";import"./redux-vendor-Wiuug2_S.js";const xe=re({title:o().required("Incident title is required").min(5,"Title must be at least 5 characters").max(100,"Title must not exceed 100 characters"),description:o().required("Description is required").min(10,"Description must be at least 10 characters").max(1e3,"Description must not exceed 1000 characters"),severity:o().required("Severity level is required").oneOf(["Minor","Moderate","Serious","Critical"],"Please select a valid severity level"),incidentDate:o().required("Incident date and time is required"),location:o().required("Location is required").min(3,"Location must be at least 3 characters"),category:o().required("Incident category is required"),latitude:O().optional(),longitude:O().optional(),involvedPersons:o().optional().max(500,"Involved persons description must not exceed 500 characters"),immediateActions:o().optional().max(500,"Immediate actions must not exceed 500 characters")}),be=()=>{var z;const f=te(),[S,A]=u.useState(!1),[L,C]=u.useState(null),[c,h]=u.useState(null),[M,p]=u.useState(!1),[N,T]=u.useState([]),{register:l,handleSubmit:U,formState:{errors:s,isDirty:q},watch:W,setValue:x,getValues:B}=ae({resolver:ne(xe),defaultValues:{title:"",description:"",severity:"Minor",incidentDate:new Date().toISOString().slice(0,16),location:"",category:"",involvedPersons:"",immediateActions:""}});u.useEffect(()=>{if(!q)return;const i=setInterval(()=>{const t=B();h("saving"),setTimeout(()=>{try{localStorage.setItem("incident_draft",JSON.stringify(t)),h("saved"),setTimeout(()=>h(null),2e3)}catch{h("error"),setTimeout(()=>h(null),3e3)}},500)},3e4);return()=>clearInterval(i)},[q,B]),u.useEffect(()=>{const i=localStorage.getItem("incident_draft");if(i)try{const t=JSON.parse(i);Object.keys(t).forEach(d=>{x(d,t[d])})}catch(t){console.warn("Failed to load draft:",t)}},[x]);const K=()=>{p(!0),navigator.geolocation?navigator.geolocation.getCurrentPosition(i=>{x("latitude",i.coords.latitude),x("longitude",i.coords.longitude),x("location",`GPS: ${i.coords.latitude.toFixed(6)}, ${i.coords.longitude.toFixed(6)}`),p(!1)},i=>{console.error("Geolocation error:",i),p(!1),alert("Unable to get your location. Please enter the location manually.")}):(p(!1),alert("Geolocation is not supported by this browser."))},$=i=>{const t=Array.from(i.target.files||[]),d=t.filter(m=>{const X=m.type.startsWith("image/"),Y=m.type.startsWith("video/"),ee=m.size<=50*1024*1024;return(X||Y)&&ee});d.length!==t.length&&alert("Some files were skipped. Only images and videos under 50MB are allowed."),T(m=>[...m,...d])},H=i=>{T(t=>t.filter((d,m)=>m!==i))},J=async i=>{A(!0),C(null);try{console.log("Submitting incident:",i),console.log("Uploaded files:",N),await new Promise(t=>setTimeout(t,2e3)),localStorage.removeItem("incident_draft"),f("/incidents",{state:{message:"Incident reported successfully!",type:"success"}})}catch(t){C("Failed to submit incident report. Please try again."),console.error("Submit error:",t)}finally{A(!1)}},Q=[{value:"student_injury",label:"Student Injury (Sports, Playground, Classroom)"},{value:"staff_injury",label:"Staff/Teacher Injury"},{value:"visitor_incident",label:"Visitor Incident"},{value:"property_damage",label:"Property Damage"},{value:"environmental",label:"Environmental Incident"},{value:"security",label:"Security Incident"},{value:"near_miss",label:"Near-Miss Event"},{value:"medical_emergency",label:"Medical Emergency"},{value:"lab_accident",label:"Laboratory Accident"},{value:"transportation",label:"Transportation Incident"}],Z=["Main Building - Ground Floor","Main Building - 1st Floor","Main Building - 2nd Floor","Science Wing - Chemistry Lab","Science Wing - Physics Lab","Science Wing - Biology Lab","Library - Main Hall","Library - Study Rooms","Gymnasium - Main Court","Gymnasium - Equipment Room","Cafeteria - Dining Area","Cafeteria - Kitchen","Playground - Primary","Playground - Secondary","Swimming Pool Area","Parking Area","Sports Field","Other (specify in description)"];return e.jsx(v,{children:e.jsx(n,{xs:12,children:e.jsxs(le,{className:"shadow-sm",children:[e.jsxs(oe,{className:"d-flex justify-content-between align-items-center",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"mb-0",style:{color:"var(--harmoni-charcoal)",fontFamily:"Poppins, sans-serif"},children:[e.jsx(a,{icon:E,size:"lg",className:"me-2 text-warning"}),"Report New Incident"]}),e.jsx("small",{className:"text-muted",children:"Fill out all required information to report an incident"})]}),e.jsxs("div",{className:"d-flex align-items-center gap-2",children:[c&&e.jsxs(V,{color:c==="saved"?"success":c==="saving"?"info":"danger",className:"d-flex align-items-center",children:[c==="saving"&&e.jsx(F,{size:"sm",className:"me-1"}),e.jsx(a,{icon:c==="saved"?_:I,size:"sm",className:"me-1"}),c==="saved"?"Auto-saved":c==="saving"?"Saving...":"Save failed"]}),e.jsxs(j,{color:"secondary",variant:"outline",onClick:()=>f("/incidents"),children:[e.jsx(a,{icon:ie,size:"sm",className:"me-1"}),"Back to List"]})]})]}),e.jsx(ce,{children:e.jsxs(de,{onSubmit:U(J),children:[L&&e.jsx(me,{color:"danger",dismissible:!0,onClose:()=>C(null),children:L}),e.jsxs(ue,{color:"info",className:"mb-4",children:[e.jsx(a,{icon:I,className:"me-2"}),e.jsx("strong",{children:"Important:"})," Report incidents as soon as possible. All serious incidents must be reported within 2 hours according to Indonesian regulations."]}),e.jsxs(he,{children:[e.jsxs(g,{itemKey:1,children:[e.jsx(y,{children:e.jsx("strong",{children:"1. Basic Information"})}),e.jsxs(b,{children:[e.jsxs(v,{className:"mb-3",children:[e.jsxs(n,{md:6,children:[e.jsx(r,{htmlFor:"title",children:"Incident Title *"}),e.jsx(P,{id:"title",...l("title"),invalid:!!s.title,placeholder:"Brief description of what happened"}),s.title&&e.jsx("div",{className:"invalid-feedback d-block",children:s.title.message})]}),e.jsxs(n,{md:3,children:[e.jsx(r,{htmlFor:"severity",children:"Severity Level *"}),e.jsxs(k,{id:"severity",...l("severity"),invalid:!!s.severity,children:[e.jsx("option",{value:"Minor",children:"Minor"}),e.jsx("option",{value:"Moderate",children:"Moderate"}),e.jsx("option",{value:"Serious",children:"Serious"}),e.jsx("option",{value:"Critical",children:"Critical"})]}),s.severity&&e.jsx("div",{className:"invalid-feedback d-block",children:s.severity.message})]}),e.jsxs(n,{md:3,children:[e.jsx(r,{htmlFor:"category",children:"Category *"}),e.jsxs(k,{id:"category",...l("category"),invalid:!!s.category,children:[e.jsx("option",{value:"",children:"Select category..."}),Q.map(i=>e.jsx("option",{value:i.value,children:i.label},i.value))]}),s.category&&e.jsx("div",{className:"invalid-feedback d-block",children:s.category.message})]})]}),e.jsx(v,{className:"mb-3",children:e.jsxs(n,{xs:12,children:[e.jsx(r,{htmlFor:"description",children:"Detailed Description *"}),e.jsx(w,{id:"description",rows:4,...l("description"),invalid:!!s.description,placeholder:"Describe what happened in detail. Include any relevant circumstances, conditions, or factors that may have contributed to the incident."}),s.description&&e.jsx("div",{className:"invalid-feedback d-block",children:s.description.message}),e.jsxs("small",{className:"text-muted",children:[((z=W("description"))==null?void 0:z.length)||0,"/1000 characters"]})]})})]})]}),e.jsxs(g,{itemKey:2,children:[e.jsx(y,{children:e.jsx("strong",{children:"2. Location and Time"})}),e.jsx(b,{children:e.jsxs(v,{className:"mb-3",children:[e.jsxs(n,{md:6,children:[e.jsx(r,{htmlFor:"incidentDate",children:"Incident Date and Time *"}),e.jsxs(D,{children:[e.jsx(R,{children:e.jsx(a,{icon:se})}),e.jsx(P,{id:"incidentDate",type:"datetime-local",...l("incidentDate"),invalid:!!s.incidentDate})]}),s.incidentDate&&e.jsx("div",{className:"invalid-feedback d-block",children:s.incidentDate.message})]}),e.jsxs(n,{md:6,children:[e.jsx(r,{htmlFor:"location",children:"Location *"}),e.jsxs(D,{children:[e.jsxs(k,{id:"location",...l("location"),invalid:!!s.location,children:[e.jsx("option",{value:"",children:"Select location..."}),Z.map(i=>e.jsx("option",{value:i,children:i},i))]}),e.jsx(j,{type:"button",color:"primary",variant:"outline",onClick:K,disabled:M,children:M?e.jsx(F,{size:"sm"}):e.jsx(a,{icon:E})})]}),s.location&&e.jsx("div",{className:"invalid-feedback d-block",children:s.location.message}),e.jsx("small",{className:"text-muted",children:"Click the location button to use GPS coordinates"})]})]})})]}),e.jsxs(g,{itemKey:3,children:[e.jsx(y,{children:e.jsx("strong",{children:"3. Additional Details"})}),e.jsx(b,{children:e.jsxs(v,{className:"mb-3",children:[e.jsxs(n,{md:6,children:[e.jsx(r,{htmlFor:"involvedPersons",children:"Involved Persons"}),e.jsx(w,{id:"involvedPersons",rows:3,...l("involvedPersons"),invalid:!!s.involvedPersons,placeholder:"List any persons involved (witnesses, injured parties, etc.)"}),s.involvedPersons&&e.jsx("div",{className:"invalid-feedback d-block",children:s.involvedPersons.message})]}),e.jsxs(n,{md:6,children:[e.jsx(r,{htmlFor:"immediateActions",children:"Immediate Actions Taken"}),e.jsx(w,{id:"immediateActions",rows:3,...l("immediateActions"),invalid:!!s.immediateActions,placeholder:"Describe any immediate actions taken to address the incident"}),s.immediateActions&&e.jsx("div",{className:"invalid-feedback d-block",children:s.immediateActions.message})]})]})})]}),e.jsxs(g,{itemKey:4,children:[e.jsx(y,{children:e.jsx("strong",{children:"4. Evidence (Photos/Videos)"})}),e.jsxs(b,{children:[e.jsxs("div",{className:"mb-3",children:[e.jsx(r,{htmlFor:"files",children:"Upload Photos or Videos"}),e.jsxs(D,{children:[e.jsx(P,{id:"files",type:"file",multiple:!0,accept:"image/*,video/*",onChange:$}),e.jsx(R,{children:e.jsx(a,{icon:G})})]}),e.jsx("small",{className:"text-muted",children:"Maximum 5 photos, 2-minute videos. Files must be under 50MB each."})]}),N.length>0&&e.jsxs("div",{children:[e.jsx("h6",{children:"Uploaded Files:"}),N.map((i,t)=>e.jsxs(V,{color:"success",className:"me-2 mb-2 d-inline-flex align-items-center",children:[e.jsx(a,{icon:G,size:"sm",className:"me-1"}),i.name,e.jsx(j,{size:"sm",color:"light",className:"ms-2 p-0",style:{width:"16px",height:"16px"},onClick:()=>H(t),children:"×"})]},t))]})]})]})]}),e.jsxs("div",{className:"d-flex justify-content-between align-items-center mt-4 pt-3 border-top",children:[e.jsx("div",{className:"text-muted",children:e.jsxs("small",{children:[e.jsx(a,{icon:I,size:"sm",className:"me-1"}),"Form auto-saves every 30 seconds"]})}),e.jsxs("div",{className:"d-flex gap-2",children:[e.jsx(j,{type:"button",color:"secondary",variant:"outline",onClick:()=>f("/incidents"),disabled:S,children:"Cancel"}),e.jsx(j,{type:"submit",color:"primary",disabled:S,className:"d-flex align-items-center",children:S?e.jsxs(e.Fragment,{children:[e.jsx(F,{size:"sm",className:"me-2"}),"Submitting..."]}):e.jsxs(e.Fragment,{children:[e.jsx(a,{icon:_,size:"sm",className:"me-2"}),"Submit Report"]})})]})]})]})})]})})})};export{be as default};
