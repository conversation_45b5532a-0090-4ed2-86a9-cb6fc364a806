const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Dashboard-CtaojPyc.js","assets/coreui-vendor-CeiuoDvP.js","assets/react-vendor-Dc0cLFd6.js","assets/redux-vendor-Wiuug2_S.js","assets/Login-D-vNQc28.js","assets/index.esm-irDu7M73.js","assets/IncidentList-BeYcGaGD.js","assets/CreateIncident-7ZWZLZ-w.js"])))=>i.map(i=>d[i]);
var lr=Object.defineProperty;var dr=(e,t,n)=>t in e?lr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var at=(e,t,n)=>dr(e,typeof t!="symbol"?t+"":t,n);import{r as N,b as fr,u as hr,N as ct,O as Mt,c as Qt,d as We,R as pe,B as pr,e as mr,f as Y}from"./react-vendor-Dc0cLFd6.js";import{w as ut,n as Ot,f as Lt,i as xe,e as yr,c as me,a as Dt,p as et,b as lt,d as oe,g as ge,h as Re,j as tt,k as ae,l as dt,m as gr,S as _t,o as Fe,q as rt,r as Nt,s as vr,t as Ht,u as Sr,v as ft,x as br,y as xr,z as jr,A as Ir,B as Ft,C as Vt,D as be,E as Ar,P as wr}from"./redux-vendor-Wiuug2_S.js";import{C as Pr,a as Cr,b as Er,c as J,d as fe,e as ke,f as Rr,g as kr,h as ht,i as Tr,j as G,k as qr,l as Mr,m as pt,n as mt,o as Qr,p as yt,q as gt,r as le,s as Ve,t as Or,u as Lr,v as Ut}from"./coreui-vendor-CeiuoDvP.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))p(d);new MutationObserver(d=>{for(const g of d)if(g.type==="childList")for(const y of g.addedNodes)y.tagName==="LINK"&&y.rel==="modulepreload"&&p(y)}).observe(document,{childList:!0,subtree:!0});function n(d){const g={};return d.integrity&&(g.integrity=d.integrity),d.referrerPolicy&&(g.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?g.credentials="include":d.crossOrigin==="anonymous"?g.credentials="omit":g.credentials="same-origin",g}function p(d){if(d.ep)return;d.ep=!0;const g=n(d);fetch(d.href,g)}})();var zt={exports:{}},Qe={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dr=N,_r=Symbol.for("react.element"),Nr=Symbol.for("react.fragment"),Hr=Object.prototype.hasOwnProperty,Fr=Dr.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Vr={key:!0,ref:!0,__self:!0,__source:!0};function $t(e,t,n){var p,d={},g=null,y=null;n!==void 0&&(g=""+n),t.key!==void 0&&(g=""+t.key),t.ref!==void 0&&(y=t.ref);for(p in t)Hr.call(t,p)&&!Vr.hasOwnProperty(p)&&(d[p]=t[p]);if(e&&e.defaultProps)for(p in t=e.defaultProps,t)d[p]===void 0&&(d[p]=t[p]);return{$$typeof:_r,type:e,key:g,ref:y,props:d,_owner:Fr.current}}Qe.Fragment=Nr;Qe.jsx=$t;Qe.jsxs=$t;zt.exports=Qe;var l=zt.exports,Je={},vt=fr;Je.createRoot=vt.createRoot,Je.hydrateRoot=vt.hydrateRoot;const Ur="modulepreload",zr=function(e){return"/"+e},St={},Oe=function(t,n,p){let d=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const y=document.querySelector("meta[property=csp-nonce]"),E=(y==null?void 0:y.nonce)||(y==null?void 0:y.getAttribute("nonce"));d=Promise.allSettled(n.map(P=>{if(P=zr(P),P in St)return;St[P]=!0;const I=P.endsWith(".css"),D=I?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${P}"]${D}`))return;const w=document.createElement("link");if(w.rel=I?"stylesheet":Ur,I||(w.as="script"),w.crossOrigin="",w.href=P,E&&w.setAttribute("nonce",E),document.head.appendChild(w),I)return new Promise((b,m)=>{w.addEventListener("load",b),w.addEventListener("error",()=>m(new Error(`Unable to preload CSS for ${P}`)))})}))}function g(y){const E=new Event("vite:preloadError",{cancelable:!0});if(E.payload=y,window.dispatchEvent(E),!E.defaultPrevented)throw y}return d.then(y=>{for(const E of y||[])E.status==="rejected"&&g(E.reason);return t().catch(g)})};var $r=class extends Error{constructor(t){super(t[0].message);at(this,"issues");this.name="SchemaError",this.issues=t}},Bt=(e=>(e.uninitialized="uninitialized",e.pending="pending",e.fulfilled="fulfilled",e.rejected="rejected",e))(Bt||{});function bt(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}var xt=xe;function Kt(e,t){if(e===t||!(xt(e)&&xt(t)||Array.isArray(e)&&Array.isArray(t)))return t;const n=Object.keys(t),p=Object.keys(e);let d=n.length===p.length;const g=Array.isArray(t)?[]:{};for(const y of n)g[y]=Kt(e[y],t[y]),d&&(d=e[y]===g[y]);return d?e:g}function he(e){let t=0;for(const n in e)t++;return t}var jt=e=>[].concat(...e);function Br(e){return new RegExp("(^|:)//").test(e)}function Kr(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function Te(e){return e!=null}function Zr(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var Wr=e=>e.replace(/\/$/,""),Jr=e=>e.replace(/^\//,"");function Gr(e,t){if(!e)return t;if(!t)return e;if(Br(t))return t;const n=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=Wr(e),t=Jr(t),`${e}${n}${t}`}function Yr(e,t,n){return e.has(t)?e.get(t):e.set(t,n).get(t)}var It=(...e)=>fetch(...e),Xr=e=>e.status>=200&&e.status<=299,en=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function At(e){if(!xe(e))return e;const t={...e};for(const[n,p]of Object.entries(t))p===void 0&&delete t[n];return t}function Zt({baseUrl:e,prepareHeaders:t=w=>w,fetchFn:n=It,paramsSerializer:p,isJsonContentType:d=en,jsonContentType:g="application/json",jsonReplacer:y,timeout:E,responseHandler:P,validateStatus:I,...D}={}){return typeof fetch>"u"&&n===It&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(b,m,R)=>{const{getState:T,extra:A,endpoint:v,forced:M,type:h}=m;let r,{url:a,headers:S=new Headers(D.headers),params:x=void 0,responseHandler:j=P??"json",validateStatus:f=I??Xr,timeout:s=E,...i}=typeof b=="string"?{url:b}:b,o,c=m.signal;s&&(o=new AbortController,m.signal.addEventListener("abort",o.abort),c=o.signal);let u={...D,signal:c,...i};S=new Headers(At(S)),u.headers=await t(S,{getState:T,arg:b,extra:A,endpoint:v,forced:M,type:h,extraOptions:R})||S;const C=V=>typeof V=="object"&&(xe(V)||Array.isArray(V)||typeof V.toJSON=="function");if(!u.headers.has("content-type")&&C(u.body)&&u.headers.set("content-type",g),C(u.body)&&d(u.headers)&&(u.body=JSON.stringify(u.body,y)),x){const V=~a.indexOf("?")?"&":"?",O=p?p(x):new URLSearchParams(At(x));a+=V+O}a=Gr(e,a);const k=new Request(a,u);r={request:new Request(a,u)};let L,q=!1,_=o&&setTimeout(()=>{q=!0,o.abort()},s);try{L=await n(k)}catch(V){return{error:{status:q?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(V)},meta:r}}finally{_&&clearTimeout(_),o==null||o.signal.removeEventListener("abort",o.abort)}const H=L.clone();r.response=H;let U,K="";try{let V;if(await Promise.all([w(L,j).then(O=>U=O,O=>V=O),H.text().then(O=>K=O,()=>{})]),V)throw V}catch(V){return{error:{status:"PARSING_ERROR",originalStatus:L.status,data:K,error:String(V)},meta:r}}return f(L,U)?{data:U,meta:r}:{error:{status:L.status,data:U},meta:r}};async function w(b,m){if(typeof m=="function")return m(b);if(m==="content-type"&&(m=d(b.headers)?"json":"text"),m==="json"){const R=await b.text();return R.length?JSON.parse(R):null}return b.text()}}var wt=class{constructor(e,t=void 0){this.value=e,this.meta=t}},Le=me("__rtkq/focused"),nt=me("__rtkq/unfocused"),De=me("__rtkq/online"),it=me("__rtkq/offline"),Ue=!1;function tn(e,t){function n(){const p=()=>e(Le()),d=()=>e(nt()),g=()=>e(De()),y=()=>e(it()),E=()=>{window.document.visibilityState==="visible"?p():d()};return Ue||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",E,!1),window.addEventListener("focus",p,!1),window.addEventListener("online",g,!1),window.addEventListener("offline",y,!1),Ue=!0),()=>{window.removeEventListener("focus",p),window.removeEventListener("visibilitychange",E),window.removeEventListener("online",g),window.removeEventListener("offline",y),Ue=!1}}return n()}function _e(e){return e.type==="query"}function rn(e){return e.type==="mutation"}function Ne(e){return e.type==="infinitequery"}function qe(e){return _e(e)||Ne(e)}function st(e,t,n,p,d,g){return nn(e)?e(t,n,p,d).filter(Te).map(Ge).map(g):Array.isArray(e)?e.map(Ge).map(g):[]}function nn(e){return typeof e=="function"}function Ge(e){return typeof e=="string"?{type:e}:e}function sn(e,t){return e.catch(t)}var je=Symbol("forceQueryFn"),Ye=e=>typeof e[je]=="function";function on({serializeQueryArgs:e,queryThunk:t,infiniteQueryThunk:n,mutationThunk:p,api:d,context:g}){const y=new Map,E=new Map,{unsubscribeQueryResult:P,removeMutationResult:I,updateSubscriptionOptions:D}=d.internalActions;return{buildInitiateQuery:A,buildInitiateInfiniteQuery:v,buildInitiateMutation:M,getRunningQueryThunk:w,getRunningMutationThunk:b,getRunningQueriesThunk:m,getRunningMutationsThunk:R};function w(h,r){return a=>{var j;const S=g.endpointDefinitions[h],x=e({queryArgs:r,endpointDefinition:S,endpointName:h});return(j=y.get(a))==null?void 0:j[x]}}function b(h,r){return a=>{var S;return(S=E.get(a))==null?void 0:S[r]}}function m(){return h=>Object.values(y.get(h)||{}).filter(Te)}function R(){return h=>Object.values(E.get(h)||{}).filter(Te)}function T(h,r){const a=(S,{subscribe:x=!0,forceRefetch:j,subscriptionOptions:f,[je]:s,...i}={})=>(o,c)=>{var $;const u=e({queryArgs:S,endpointDefinition:r,endpointName:h});let C;const k={...i,type:"query",subscribe:x,forceRefetch:j,subscriptionOptions:f,endpointName:h,originalArgs:S,queryCacheKey:u,[je]:s};if(_e(r))C=t(k);else{const{direction:F,initialPageParam:W}=i;C=n({...k,direction:F,initialPageParam:W})}const Q=d.endpoints[h].select(S),L=o(C),q=Q(c()),{requestId:_,abort:H}=L,U=q.requestId!==_,K=($=y.get(o))==null?void 0:$[u],V=()=>Q(c()),O=Object.assign(s?L.then(V):U&&!K?Promise.resolve(q):Promise.all([K,L]).then(V),{arg:S,requestId:_,subscriptionOptions:f,queryCacheKey:u,abort:H,async unwrap(){const F=await O;if(F.isError)throw F.error;return F.data},refetch:()=>o(a(S,{subscribe:!1,forceRefetch:!0})),unsubscribe(){x&&o(P({queryCacheKey:u,requestId:_}))},updateSubscriptionOptions(F){O.subscriptionOptions=F,o(D({endpointName:h,requestId:_,queryCacheKey:u,options:F}))}});if(!K&&!U&&!s){const F=Yr(y,o,{});F[u]=O,O.then(()=>{delete F[u],he(F)||y.delete(o)})}return O};return a}function A(h,r){return T(h,r)}function v(h,r){return T(h,r)}function M(h){return(r,{track:a=!0,fixedCacheKey:S}={})=>(x,j)=>{const f=p({type:"mutation",endpointName:h,originalArgs:r,track:a,fixedCacheKey:S}),s=x(f),{requestId:i,abort:o,unwrap:c}=s,u=sn(s.unwrap().then(L=>({data:L})),L=>({error:L})),C=()=>{x(I({requestId:i,fixedCacheKey:S}))},k=Object.assign(u,{arg:s.arg,requestId:i,abort:o,unwrap:c,reset:C}),Q=E.get(x)||{};return E.set(x,Q),Q[i]=k,k.then(()=>{delete Q[i],he(Q)||E.delete(x)}),S&&(Q[S]=k,k.then(()=>{Q[S]===k&&(delete Q[S],he(Q)||E.delete(x))})),k}}}var Wt=class extends $r{constructor(e,t,n,p){super(e),this.value=t,this.schemaName=n,this._bqMeta=p}};async function se(e,t,n,p){const d=await e["~standard"].validate(t);if(d.issues)throw new Wt(d.issues,t,n,p);return d.value}function an(e){return e}var ve=(e={})=>({...e,[_t]:!0});function cn({reducerPath:e,baseQuery:t,context:{endpointDefinitions:n},serializeQueryArgs:p,api:d,assertTagType:g,selectors:y,onSchemaFailure:E,catchSchemaFailure:P,skipSchemaValidation:I}){const D=(i,o,c,u)=>(C,k)=>{const Q=n[i],L=p({queryArgs:o,endpointDefinition:Q,endpointName:i});if(C(d.internalActions.queryResultPatched({queryCacheKey:L,patches:c})),!u)return;const q=d.endpoints[i].select(o)(k()),_=st(Q.providesTags,q.data,void 0,o,{},g);C(d.internalActions.updateProvidedBy([{queryCacheKey:L,providedTags:_}]))};function w(i,o,c=0){const u=[o,...i];return c&&u.length>c?u.slice(0,-1):u}function b(i,o,c=0){const u=[...i,o];return c&&u.length>c?u.slice(1):u}const m=(i,o,c,u=!0)=>(C,k)=>{const L=d.endpoints[i].select(o)(k()),q={patches:[],inversePatches:[],undo:()=>C(d.util.patchQueryData(i,o,q.inversePatches,u))};if(L.status==="uninitialized")return q;let _;if("data"in L)if(vr(L.data)){const[H,U,K]=Ht(L.data,c);q.patches.push(...U),q.inversePatches.push(...K),_=H}else _=c(L.data),q.patches.push({op:"replace",path:[],value:_}),q.inversePatches.push({op:"replace",path:[],value:L.data});return q.patches.length===0||C(d.util.patchQueryData(i,o,q.patches,u)),q},R=(i,o,c)=>u=>u(d.endpoints[i].initiate(o,{subscribe:!1,forceRefetch:!0,[je]:()=>({data:c})})),T=(i,o)=>i.query&&i[o]?i[o]:an,A=async(i,{signal:o,abort:c,rejectWithValue:u,fulfillWithValue:C,dispatch:k,getState:Q,extra:L})=>{var U,K;const q=n[i.endpointName],{metaSchema:_,skipSchemaValidation:H=I}=q;try{let V=T(q,"transformResponse");const O={signal:o,abort:c,dispatch:k,getState:Q,extra:L,endpoint:i.endpointName,type:i.type,forced:i.type==="query"?v(i,Q()):void 0,queryCacheKey:i.type==="query"?i.queryCacheKey:void 0},$=i.type==="query"?i[je]:void 0;let F;const W=async(B,z,Z,ce)=>{if(z==null&&B.pages.length)return Promise.resolve({data:B});const ee={queryArg:i.originalArgs,pageParam:z},ue=await re(ee),te=ce?w:b;return{data:{pages:te(B.pages,ue.data,Z),pageParams:te(B.pageParams,z,Z)},meta:ue.meta}};async function re(B){let z;const{extraOptions:Z,argSchema:ce,rawResponseSchema:ee,responseSchema:ue}=q;if(ce&&!H&&(B=await se(ce,B,"argSchema",{})),$?z=$():q.query?z=await t(q.query(B),O,Z):z=await q.queryFn(B,O,Z,ye=>t(ye,O,Z)),typeof process<"u",z.error)throw new wt(z.error,z.meta);let{data:te}=z;ee&&!H&&(te=await se(ee,z.data,"rawResponseSchema",z.meta));let ie=await V(te,z.meta,B);return ue&&!H&&(ie=await se(ue,ie,"responseSchema",z.meta)),{...z,data:ie}}if(i.type==="query"&&"infiniteQueryOptions"in q){const{infiniteQueryOptions:B}=q,{maxPages:z=1/0}=B;let Z;const ce={pages:[],pageParams:[]},ee=(U=y.selectQueryEntry(Q(),i.queryCacheKey))==null?void 0:U.data,te=v(i,Q())&&!i.direction||!ee?ce:ee;if("direction"in i&&i.direction&&te.pages.length){const ie=i.direction==="backward",He=(ie?Jt:Xe)(B,te,i.originalArgs);Z=await W(te,He,z,ie)}else{const{initialPageParam:ie=B.initialPageParam}=i,ye=(ee==null?void 0:ee.pageParams)??[],He=ye[0]??ie,cr=ye.length;Z=await W(te,He,z),$&&(Z={data:Z.data.pages[0]});for(let ot=1;ot<cr;ot++){const ur=Xe(B,Z.data,i.originalArgs);Z=await W(Z.data,ur,z)}}F=Z}else F=await re(i.originalArgs);return _&&!H&&F.meta&&(F.meta=await se(_,F.meta,"metaSchema",F.meta)),C(F.data,ve({fulfilledTimeStamp:Date.now(),baseQueryMeta:F.meta}))}catch(V){let O=V;if(O instanceof wt){let $=T(q,"transformErrorResponse");const{rawErrorResponseSchema:F,errorResponseSchema:W}=q;let{value:re,meta:B}=O;try{F&&!H&&(re=await se(F,re,"rawErrorResponseSchema",B)),_&&!H&&(B=await se(_,B,"metaSchema",B));let z=await $(re,B,i.originalArgs);return W&&!H&&(z=await se(W,z,"errorResponseSchema",B)),u(z,ve({baseQueryMeta:B}))}catch(z){O=z}}try{if(O instanceof Wt){const $={endpoint:i.endpointName,arg:i.originalArgs,type:i.type,queryCacheKey:i.type==="query"?i.queryCacheKey:void 0};(K=q.onSchemaFailure)==null||K.call(q,O,$),E==null||E(O,$);const{catchSchemaFailure:F=P}=q;if(F)return u(F(O,$),ve({baseQueryMeta:O._bqMeta}))}}catch($){O=$}throw console.error(O),O}};function v(i,o){const c=y.selectQueryEntry(o,i.queryCacheKey),u=y.selectConfig(o).refetchOnMountOrArgChange,C=c==null?void 0:c.fulfilledTimeStamp,k=i.forceRefetch??(i.subscribe&&u);return k?k===!0||(Number(new Date)-Number(C))/1e3>=k:!1}const M=()=>lt(`${e}/executeQuery`,A,{getPendingMeta({arg:o}){const c=n[o.endpointName];return ve({startedTimeStamp:Date.now(),...Ne(c)?{direction:o.direction}:{}})},condition(o,{getState:c}){var H;const u=c(),C=y.selectQueryEntry(u,o.queryCacheKey),k=C==null?void 0:C.fulfilledTimeStamp,Q=o.originalArgs,L=C==null?void 0:C.originalArgs,q=n[o.endpointName],_=o.direction;return Ye(o)?!0:(C==null?void 0:C.status)==="pending"?!1:v(o,u)||_e(q)&&((H=q==null?void 0:q.forceRefetch)!=null&&H.call(q,{currentArg:Q,previousArg:L,endpointState:C,state:u}))?!0:!(k&&!_)},dispatchConditionRejection:!0}),h=M(),r=M(),a=lt(`${e}/executeMutation`,A,{getPendingMeta(){return ve({startedTimeStamp:Date.now()})}}),S=i=>"force"in i,x=i=>"ifOlderThan"in i,j=(i,o,c)=>(u,C)=>{const k=S(c)&&c.force,Q=x(c)&&c.ifOlderThan,L=(_=!0)=>{const H={forceRefetch:_,isPrefetch:!0};return d.endpoints[i].initiate(o,H)},q=d.endpoints[i].select(o)(C());if(k)u(L());else if(Q){const _=q==null?void 0:q.fulfilledTimeStamp;if(!_){u(L());return}(Number(new Date)-Number(new Date(_)))/1e3>=Q&&u(L())}else u(L(!1))};function f(i){return o=>{var c,u;return((u=(c=o==null?void 0:o.meta)==null?void 0:c.arg)==null?void 0:u.endpointName)===i}}function s(i,o){return{matchPending:Fe(Nt(i),f(o)),matchFulfilled:Fe(ae(i),f(o)),matchRejected:Fe(rt(i),f(o))}}return{queryThunk:h,mutationThunk:a,infiniteQueryThunk:r,prefetch:j,updateQueryData:m,upsertQueryData:R,patchQueryData:D,buildMatchThunkActions:s}}function Xe(e,{pages:t,pageParams:n},p){const d=t.length-1;return e.getNextPageParam(t[d],t,n[d],n,p)}function Jt(e,{pages:t,pageParams:n},p){var d;return(d=e.getPreviousPageParam)==null?void 0:d.call(e,t[0],t,n[0],n,p)}function Gt(e,t,n,p){return st(n[e.meta.arg.endpointName][t],ae(e)?e.payload:void 0,tt(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,p)}function Ae(e,t,n){const p=e[t];p&&n(p)}function Ie(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function Pt(e,t,n){const p=e[Ie(t)];p&&n(p)}var we={};function un({reducerPath:e,queryThunk:t,mutationThunk:n,serializeQueryArgs:p,context:{endpointDefinitions:d,apiUid:g,extractRehydrationInfo:y,hasRehydrationInfo:E},assertTagType:P,config:I}){const D=me(`${e}/resetApiState`);function w(f,s,i,o){var c;f[c=s.queryCacheKey]??(f[c]={status:"uninitialized",endpointName:s.endpointName}),Ae(f,s.queryCacheKey,u=>{u.status="pending",u.requestId=i&&u.requestId?u.requestId:o.requestId,s.originalArgs!==void 0&&(u.originalArgs=s.originalArgs),u.startedTimeStamp=o.startedTimeStamp;const C=d[o.arg.endpointName];Ne(C)&&"direction"in s&&(u.direction=s.direction)})}function b(f,s,i,o){Ae(f,s.arg.queryCacheKey,c=>{if(c.requestId!==s.requestId&&!o)return;const{merge:u}=d[s.arg.endpointName];if(c.status="fulfilled",u)if(c.data!==void 0){const{fulfilledTimeStamp:C,arg:k,baseQueryMeta:Q,requestId:L}=s;let q=et(c.data,_=>u(_,i,{arg:k.originalArgs,baseQueryMeta:Q,fulfilledTimeStamp:C,requestId:L}));c.data=q}else c.data=i;else c.data=d[s.arg.endpointName].structuralSharing??!0?Kt(br(c.data)?xr(c.data):c.data,i):i;delete c.error,c.fulfilledTimeStamp=s.fulfilledTimeStamp})}const m=oe({name:`${e}/queries`,initialState:we,reducers:{removeQueryResult:{reducer(f,{payload:{queryCacheKey:s}}){delete f[s]},prepare:ge()},cacheEntriesUpserted:{reducer(f,s){for(const i of s.payload){const{queryDescription:o,value:c}=i;w(f,o,!0,{arg:o,requestId:s.meta.requestId,startedTimeStamp:s.meta.timestamp}),b(f,{arg:o,requestId:s.meta.requestId,fulfilledTimeStamp:s.meta.timestamp,baseQueryMeta:{}},c,!0)}},prepare:f=>({payload:f.map(o=>{const{endpointName:c,arg:u,value:C}=o,k=d[c];return{queryDescription:{type:"query",endpointName:c,originalArgs:o.arg,queryCacheKey:p({queryArgs:u,endpointDefinition:k,endpointName:c})},value:C}}),meta:{[_t]:!0,requestId:Ot(),timestamp:Date.now()}})},queryResultPatched:{reducer(f,{payload:{queryCacheKey:s,patches:i}}){Ae(f,s,o=>{o.data=dt(o.data,i.concat())})},prepare:ge()}},extraReducers(f){f.addCase(t.pending,(s,{meta:i,meta:{arg:o}})=>{const c=Ye(o);w(s,o,c,i)}).addCase(t.fulfilled,(s,{meta:i,payload:o})=>{const c=Ye(i.arg);b(s,i,o,c)}).addCase(t.rejected,(s,{meta:{condition:i,arg:o,requestId:c},error:u,payload:C})=>{Ae(s,o.queryCacheKey,k=>{if(!i){if(k.requestId!==c)return;k.status="rejected",k.error=C??u}})}).addMatcher(E,(s,i)=>{const{queries:o}=y(i);for(const[c,u]of Object.entries(o))((u==null?void 0:u.status)==="fulfilled"||(u==null?void 0:u.status)==="rejected")&&(s[c]=u)})}}),R=oe({name:`${e}/mutations`,initialState:we,reducers:{removeMutationResult:{reducer(f,{payload:s}){const i=Ie(s);i in f&&delete f[i]},prepare:ge()}},extraReducers(f){f.addCase(n.pending,(s,{meta:i,meta:{requestId:o,arg:c,startedTimeStamp:u}})=>{c.track&&(s[Ie(i)]={requestId:o,status:"pending",endpointName:c.endpointName,startedTimeStamp:u})}).addCase(n.fulfilled,(s,{payload:i,meta:o})=>{o.arg.track&&Pt(s,o,c=>{c.requestId===o.requestId&&(c.status="fulfilled",c.data=i,c.fulfilledTimeStamp=o.fulfilledTimeStamp)})}).addCase(n.rejected,(s,{payload:i,error:o,meta:c})=>{c.arg.track&&Pt(s,c,u=>{u.requestId===c.requestId&&(u.status="rejected",u.error=i??o)})}).addMatcher(E,(s,i)=>{const{mutations:o}=y(i);for(const[c,u]of Object.entries(o))((u==null?void 0:u.status)==="fulfilled"||(u==null?void 0:u.status)==="rejected")&&c!==(u==null?void 0:u.requestId)&&(s[c]=u)})}}),T={tags:{},keys:{}},A=oe({name:`${e}/invalidation`,initialState:T,reducers:{updateProvidedBy:{reducer(f,s){var i,o,c;for(const{queryCacheKey:u,providedTags:C}of s.payload){v(f,u);for(const{type:k,id:Q}of C){const L=(o=(i=f.tags)[k]??(i[k]={}))[c=Q||"__internal_without_id"]??(o[c]=[]);L.includes(u)||L.push(u)}f.keys[u]=C}},prepare:ge()}},extraReducers(f){f.addCase(m.actions.removeQueryResult,(s,{payload:{queryCacheKey:i}})=>{v(s,i)}).addMatcher(E,(s,i)=>{var c,u,C;const{provided:o}=y(i);for(const[k,Q]of Object.entries(o))for(const[L,q]of Object.entries(Q)){const _=(u=(c=s.tags)[k]??(c[k]={}))[C=L||"__internal_without_id"]??(u[C]=[]);for(const H of q)_.includes(H)||_.push(H)}}).addMatcher(Re(ae(t),tt(t)),(s,i)=>{M(s,[i])}).addMatcher(m.actions.cacheEntriesUpserted.match,(s,i)=>{const o=i.payload.map(({queryDescription:c,value:u})=>({type:"UNKNOWN",payload:u,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:c}}));M(s,o)})}});function v(f,s){var o;const i=f.keys[s]??[];for(const c of i){const u=c.type,C=c.id??"__internal_without_id",k=(o=f.tags[u])==null?void 0:o[C];k&&(f.tags[u][C]=k.filter(Q=>Q!==s))}delete f.keys[s]}function M(f,s){const i=s.map(o=>{const c=Gt(o,"providesTags",d,P),{queryCacheKey:u}=o.meta.arg;return{queryCacheKey:u,providedTags:c}});A.caseReducers.updateProvidedBy(f,A.actions.updateProvidedBy(i))}const h=oe({name:`${e}/subscriptions`,initialState:we,reducers:{updateSubscriptionOptions(f,s){},unsubscribeQueryResult(f,s){},internal_getRTKQSubscriptions(){}}}),r=oe({name:`${e}/internalSubscriptions`,initialState:we,reducers:{subscriptionsUpdated:{reducer(f,s){return dt(f,s.payload)},prepare:ge()}}}),a=oe({name:`${e}/config`,initialState:{online:Zr(),focused:Kr(),middlewareRegistered:!1,...I},reducers:{middlewareRegistered(f,{payload:s}){f.middlewareRegistered=f.middlewareRegistered==="conflict"||g!==s?"conflict":!0}},extraReducers:f=>{f.addCase(De,s=>{s.online=!0}).addCase(it,s=>{s.online=!1}).addCase(Le,s=>{s.focused=!0}).addCase(nt,s=>{s.focused=!1}).addMatcher(E,s=>({...s}))}}),S=gr({queries:m.reducer,mutations:R.reducer,provided:A.reducer,subscriptions:r.reducer,config:a.reducer}),x=(f,s)=>S(D.match(s)?void 0:f,s),j={...a.actions,...m.actions,...h.actions,...r.actions,...R.actions,...A.actions,resetApiState:D};return{reducer:x,actions:j}}var X=Symbol.for("RTKQ/skipToken"),Yt={status:"uninitialized"},Ct=et(Yt,()=>{}),Et=et(Yt,()=>{});function ln({serializeQueryArgs:e,reducerPath:t,createSelector:n}){const p=h=>Ct,d=h=>Et;return{buildQuerySelector:b,buildInfiniteQuerySelector:m,buildMutationSelector:R,selectInvalidatedBy:T,selectCachedArgsForQuery:A,selectApiState:y,selectQueries:E,selectMutations:I,selectQueryEntry:P,selectConfig:D};function g(h){return{...h,...bt(h.status)}}function y(h){return h[t]}function E(h){var r;return(r=y(h))==null?void 0:r.queries}function P(h,r){var a;return(a=E(h))==null?void 0:a[r]}function I(h){var r;return(r=y(h))==null?void 0:r.mutations}function D(h){var r;return(r=y(h))==null?void 0:r.config}function w(h,r,a){return S=>{if(S===X)return n(p,a);const x=e({queryArgs:S,endpointDefinition:r,endpointName:h});return n(f=>P(f,x)??Ct,a)}}function b(h,r){return w(h,r,g)}function m(h,r){const{infiniteQueryOptions:a}=r;function S(x){const j={...x,...bt(x.status)},{isLoading:f,isError:s,direction:i}=j,o=i==="forward",c=i==="backward";return{...j,hasNextPage:v(a,j.data,j.originalArgs),hasPreviousPage:M(a,j.data,j.originalArgs),isFetchingNextPage:f&&o,isFetchingPreviousPage:f&&c,isFetchNextPageError:s&&o,isFetchPreviousPageError:s&&c}}return w(h,r,S)}function R(){return h=>{let r;return typeof h=="object"?r=Ie(h)??X:r=h,n(r===X?d:x=>{var j,f;return((f=(j=y(x))==null?void 0:j.mutations)==null?void 0:f[r])??Et},g)}}function T(h,r){const a=h[t],S=new Set;for(const x of r.filter(Te).map(Ge)){const j=a.provided.tags[x.type];if(!j)continue;let f=(x.id!==void 0?j[x.id]:jt(Object.values(j)))??[];for(const s of f)S.add(s)}return jt(Array.from(S.values()).map(x=>{const j=a.queries[x];return j?[{queryCacheKey:x,endpointName:j.endpointName,originalArgs:j.originalArgs}]:[]}))}function A(h,r){return Object.values(E(h)).filter(a=>(a==null?void 0:a.endpointName)===r&&a.status!=="uninitialized").map(a=>a.originalArgs)}function v(h,r,a){return r?Xe(h,r,a)!=null:!1}function M(h,r,a){return!r||!h.getPreviousPageParam?!1:Jt(h,r,a)!=null}}var de=WeakMap?new WeakMap:void 0,Me=({endpointName:e,queryArgs:t})=>{let n="";const p=de==null?void 0:de.get(t);if(typeof p=="string")n=p;else{const d=JSON.stringify(t,(g,y)=>(y=typeof y=="bigint"?{$bigint:y.toString()}:y,y=xe(y)?Object.keys(y).sort().reduce((E,P)=>(E[P]=y[P],E),{}):y,y));xe(t)&&(de==null||de.set(t,d)),n=d}return`${e}(${n})`};function Xt(...e){return function(n){const p=ut(I=>{var D;return(D=n.extractRehydrationInfo)==null?void 0:D.call(n,I,{reducerPath:n.reducerPath??"api"})}),d={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...n,extractRehydrationInfo:p,serializeQueryArgs(I){let D=Me;if("serializeQueryArgs"in I.endpointDefinition){const w=I.endpointDefinition.serializeQueryArgs;D=b=>{const m=w(b);return typeof m=="string"?m:Me({...b,queryArgs:m})}}else n.serializeQueryArgs&&(D=n.serializeQueryArgs);return D(I)},tagTypes:[...n.tagTypes||[]]},g={endpointDefinitions:{},batch(I){I()},apiUid:Ot(),extractRehydrationInfo:p,hasRehydrationInfo:ut(I=>p(I)!=null)},y={injectEndpoints:P,enhanceEndpoints({addTagTypes:I,endpoints:D}){if(I)for(const w of I)d.tagTypes.includes(w)||d.tagTypes.push(w);if(D)for(const[w,b]of Object.entries(D))typeof b=="function"?b(g.endpointDefinitions[w]):Object.assign(g.endpointDefinitions[w]||{},b);return y}},E=e.map(I=>I.init(y,d,g));function P(I){const D=I.endpoints({query:w=>({...w,type:"query"}),mutation:w=>({...w,type:"mutation"}),infiniteQuery:w=>({...w,type:"infinitequery"})});for(const[w,b]of Object.entries(D)){if(I.overrideExisting!==!0&&w in g.endpointDefinitions){if(I.overrideExisting==="throw")throw new Error(Lt(39));continue}g.endpointDefinitions[w]=b;for(const m of E)m.injectEndpoint(w,b)}return y}return y.injectEndpoints({endpoints:n.endpoints})}}function ne(e,...t){return Object.assign(e,...t)}var dn=({api:e,queryThunk:t,internalState:n})=>{const p=`${e.reducerPath}/subscriptions`;let d=null,g=null;const{updateSubscriptionOptions:y,unsubscribeQueryResult:E}=e.internalActions,P=(m,R)=>{var A,v,M;if(y.match(R)){const{queryCacheKey:h,requestId:r,options:a}=R.payload;return(A=m==null?void 0:m[h])!=null&&A[r]&&(m[h][r]=a),!0}if(E.match(R)){const{queryCacheKey:h,requestId:r}=R.payload;return m[h]&&delete m[h][r],!0}if(e.internalActions.removeQueryResult.match(R))return delete m[R.payload.queryCacheKey],!0;if(t.pending.match(R)){const{meta:{arg:h,requestId:r}}=R,a=m[v=h.queryCacheKey]??(m[v]={});return a[`${r}_running`]={},h.subscribe&&(a[r]=h.subscriptionOptions??a[r]??{}),!0}let T=!1;if(t.fulfilled.match(R)||t.rejected.match(R)){const h=m[R.meta.arg.queryCacheKey]||{},r=`${R.meta.requestId}_running`;T||(T=!!h[r]),delete h[r]}if(t.rejected.match(R)){const{meta:{condition:h,arg:r,requestId:a}}=R;if(h&&r.subscribe){const S=m[M=r.queryCacheKey]??(m[M]={});S[a]=r.subscriptionOptions??S[a]??{},T=!0}}return T},I=()=>n.currentSubscriptions,b={getSubscriptions:I,getSubscriptionCount:m=>{const T=I()[m]??{};return he(T)},isRequestSubscribed:(m,R)=>{var A;const T=I();return!!((A=T==null?void 0:T[m])!=null&&A[R])}};return(m,R)=>{if(d||(d=JSON.parse(JSON.stringify(n.currentSubscriptions))),e.util.resetApiState.match(m))return d=n.currentSubscriptions={},g=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(m))return[!1,b];const T=P(n.currentSubscriptions,m);let A=!0;if(T){g||(g=setTimeout(()=>{const h=JSON.parse(JSON.stringify(n.currentSubscriptions)),[,r]=Ht(d,()=>h);R.next(e.internalActions.subscriptionsUpdated(r)),d=h,g=null},500));const v=typeof m.type=="string"&&!!m.type.startsWith(p),M=t.rejected.match(m)&&m.meta.condition&&!!m.meta.arg.subscribe;A=!v&&!M}return[A,!1]}};function fn(e){for(const t in e)return!1;return!0}var hn=2147483647/1e3-1,pn=({reducerPath:e,api:t,queryThunk:n,context:p,internalState:d,selectors:{selectQueryEntry:g,selectConfig:y}})=>{const{removeQueryResult:E,unsubscribeQueryResult:P,cacheEntriesUpserted:I}=t.internalActions,D=Re(P.match,n.fulfilled,n.rejected,I.match);function w(A){const v=d.currentSubscriptions[A];return!!v&&!fn(v)}const b={},m=(A,v,M)=>{const h=v.getState(),r=y(h);if(D(A)){let a;if(I.match(A))a=A.payload.map(S=>S.queryDescription.queryCacheKey);else{const{queryCacheKey:S}=P.match(A)?A.payload:A.meta.arg;a=[S]}R(a,v,r)}if(t.util.resetApiState.match(A))for(const[a,S]of Object.entries(b))S&&clearTimeout(S),delete b[a];if(p.hasRehydrationInfo(A)){const{queries:a}=p.extractRehydrationInfo(A);R(Object.keys(a),v,r)}};function R(A,v,M){const h=v.getState();for(const r of A){const a=g(h,r);T(r,a==null?void 0:a.endpointName,v,M)}}function T(A,v,M,h){const r=p.endpointDefinitions[v],a=(r==null?void 0:r.keepUnusedDataFor)??h.keepUnusedDataFor;if(a===1/0)return;const S=Math.max(0,Math.min(a,hn));if(!w(A)){const x=b[A];x&&clearTimeout(x),b[A]=setTimeout(()=>{w(A)||M.dispatch(E({queryCacheKey:A})),delete b[A]},S*1e3)}}return m},Rt=new Error("Promise never resolved before cacheEntryRemoved."),mn=({api:e,reducerPath:t,context:n,queryThunk:p,mutationThunk:d,internalState:g,selectors:{selectQueryEntry:y,selectApiState:E}})=>{const P=ft(p),I=ft(d),D=ae(p,d),w={};function b(v,M,h){const r=w[v];r!=null&&r.valueResolved&&(r.valueResolved({data:M,meta:h}),delete r.valueResolved)}function m(v){const M=w[v];M&&(delete w[v],M.cacheEntryRemoved())}const R=(v,M,h)=>{const r=T(v);function a(S,x,j,f){const s=y(h,x),i=y(M.getState(),x);!s&&i&&A(S,f,x,M,j)}if(p.pending.match(v))a(v.meta.arg.endpointName,r,v.meta.requestId,v.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(v))for(const{queryDescription:S,value:x}of v.payload){const{endpointName:j,originalArgs:f,queryCacheKey:s}=S;a(j,s,v.meta.requestId,f),b(s,x,{})}else if(d.pending.match(v))M.getState()[t].mutations[r]&&A(v.meta.arg.endpointName,v.meta.arg.originalArgs,r,M,v.meta.requestId);else if(D(v))b(r,v.payload,v.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(v)||e.internalActions.removeMutationResult.match(v))m(r);else if(e.util.resetApiState.match(v))for(const S of Object.keys(w))m(S)};function T(v){return P(v)?v.meta.arg.queryCacheKey:I(v)?v.meta.arg.fixedCacheKey??v.meta.requestId:e.internalActions.removeQueryResult.match(v)?v.payload.queryCacheKey:e.internalActions.removeMutationResult.match(v)?Ie(v.payload):""}function A(v,M,h,r,a){const S=n.endpointDefinitions[v],x=S==null?void 0:S.onCacheEntryAdded;if(!x)return;const j={},f=new Promise(C=>{j.cacheEntryRemoved=C}),s=Promise.race([new Promise(C=>{j.valueResolved=C}),f.then(()=>{throw Rt})]);s.catch(()=>{}),w[h]=j;const i=e.endpoints[v].select(qe(S)?M:h),o=r.dispatch((C,k,Q)=>Q),c={...r,getCacheEntry:()=>i(r.getState()),requestId:a,extra:o,updateCachedData:qe(S)?C=>r.dispatch(e.util.updateQueryData(v,M,C)):void 0,cacheDataLoaded:s,cacheEntryRemoved:f},u=x(M,c);Promise.resolve(u).catch(C=>{if(C!==Rt)throw C})}return R},yn=({api:e,context:{apiUid:t},reducerPath:n})=>(p,d)=>{e.util.resetApiState.match(p)&&d.dispatch(e.internalActions.middlewareRegistered(t))},gn=({reducerPath:e,context:t,context:{endpointDefinitions:n},mutationThunk:p,queryThunk:d,api:g,assertTagType:y,refetchQuery:E,internalState:P})=>{const{removeQueryResult:I}=g.internalActions,D=Re(ae(p),tt(p)),w=Re(ae(p,d),rt(p,d));let b=[];const m=(A,v)=>{D(A)?T(Gt(A,"invalidatesTags",n,y),v):w(A)?T([],v):g.util.invalidateTags.match(A)&&T(st(A.payload,void 0,void 0,void 0,void 0,y),v)};function R(A){var h;const{queries:v,mutations:M}=A;for(const r of[v,M])for(const a in r)if(((h=r[a])==null?void 0:h.status)==="pending")return!0;return!1}function T(A,v){const M=v.getState(),h=M[e];if(b.push(...A),h.config.invalidationBehavior==="delayed"&&R(h))return;const r=b;if(b=[],r.length===0)return;const a=g.util.selectInvalidatedBy(M,r);t.batch(()=>{const S=Array.from(a.values());for(const{queryCacheKey:x}of S){const j=h.queries[x],f=P.currentSubscriptions[x]??{};j&&(he(f)===0?v.dispatch(I({queryCacheKey:x})):j.status!=="uninitialized"&&v.dispatch(E(j)))}})}return m},vn=({reducerPath:e,queryThunk:t,api:n,refetchQuery:p,internalState:d})=>{const g={},y=(b,m)=>{(n.internalActions.updateSubscriptionOptions.match(b)||n.internalActions.unsubscribeQueryResult.match(b))&&P(b.payload,m),(t.pending.match(b)||t.rejected.match(b)&&b.meta.condition)&&P(b.meta.arg,m),(t.fulfilled.match(b)||t.rejected.match(b)&&!b.meta.condition)&&E(b.meta.arg,m),n.util.resetApiState.match(b)&&D()};function E({queryCacheKey:b},m){const R=m.getState()[e],T=R.queries[b],A=d.currentSubscriptions[b];if(!T||T.status==="uninitialized")return;const{lowestPollingInterval:v,skipPollingIfUnfocused:M}=w(A);if(!Number.isFinite(v))return;const h=g[b];h!=null&&h.timeout&&(clearTimeout(h.timeout),h.timeout=void 0);const r=Date.now()+v;g[b]={nextPollTimestamp:r,pollingInterval:v,timeout:setTimeout(()=>{(R.config.focused||!M)&&m.dispatch(p(T)),E({queryCacheKey:b},m)},v)}}function P({queryCacheKey:b},m){const T=m.getState()[e].queries[b],A=d.currentSubscriptions[b];if(!T||T.status==="uninitialized")return;const{lowestPollingInterval:v}=w(A);if(!Number.isFinite(v)){I(b);return}const M=g[b],h=Date.now()+v;(!M||h<M.nextPollTimestamp)&&E({queryCacheKey:b},m)}function I(b){const m=g[b];m!=null&&m.timeout&&clearTimeout(m.timeout),delete g[b]}function D(){for(const b of Object.keys(g))I(b)}function w(b={}){let m=!1,R=Number.POSITIVE_INFINITY;for(let T in b)b[T].pollingInterval&&(R=Math.min(b[T].pollingInterval,R),m=b[T].skipPollingIfUnfocused||m);return{lowestPollingInterval:R,skipPollingIfUnfocused:m}}return y},Sn=({api:e,context:t,queryThunk:n,mutationThunk:p})=>{const d=Nt(n,p),g=rt(n,p),y=ae(n,p),E={};return(I,D)=>{var w,b;if(d(I)){const{requestId:m,arg:{endpointName:R,originalArgs:T}}=I.meta,A=t.endpointDefinitions[R],v=A==null?void 0:A.onQueryStarted;if(v){const M={},h=new Promise((x,j)=>{M.resolve=x,M.reject=j});h.catch(()=>{}),E[m]=M;const r=e.endpoints[R].select(qe(A)?T:m),a=D.dispatch((x,j,f)=>f),S={...D,getCacheEntry:()=>r(D.getState()),requestId:m,extra:a,updateCachedData:qe(A)?x=>D.dispatch(e.util.updateQueryData(R,T,x)):void 0,queryFulfilled:h};v(T,S)}}else if(y(I)){const{requestId:m,baseQueryMeta:R}=I.meta;(w=E[m])==null||w.resolve({data:I.payload,meta:R}),delete E[m]}else if(g(I)){const{requestId:m,rejectedWithValue:R,baseQueryMeta:T}=I.meta;(b=E[m])==null||b.reject({error:I.payload??I.error,isUnhandledError:!R,meta:T}),delete E[m]}}},bn=({reducerPath:e,context:t,api:n,refetchQuery:p,internalState:d})=>{const{removeQueryResult:g}=n.internalActions,y=(P,I)=>{Le.match(P)&&E(I,"refetchOnFocus"),De.match(P)&&E(I,"refetchOnReconnect")};function E(P,I){const D=P.getState()[e],w=D.queries,b=d.currentSubscriptions;t.batch(()=>{for(const m of Object.keys(b)){const R=w[m],T=b[m];if(!T||!R)continue;(Object.values(T).some(v=>v[I]===!0)||Object.values(T).every(v=>v[I]===void 0)&&D.config[I])&&(he(T)===0?P.dispatch(g({queryCacheKey:m})):R.status!=="uninitialized"&&P.dispatch(p(R)))}})}return y};function xn(e){const{reducerPath:t,queryThunk:n,api:p,context:d}=e,{apiUid:g}=d,y={invalidateTags:me(`${t}/invalidateTags`)},E=w=>w.type.startsWith(`${t}/`),P=[yn,pn,gn,vn,mn,Sn];return{middleware:w=>{let b=!1;const R={...e,internalState:{currentSubscriptions:{}},refetchQuery:D,isThisApiSliceAction:E},T=P.map(M=>M(R)),A=dn(R),v=bn(R);return M=>h=>{if(!Sr(h))return M(h);b||(b=!0,w.dispatch(p.internalActions.middlewareRegistered(g)));const r={...w,next:M},a=w.getState(),[S,x]=A(h,r,a);let j;if(S?j=M(h):j=x,w.getState()[t]&&(v(h,r,a),E(h)||d.hasRehydrationInfo(h)))for(const f of T)f(h,r,a);return j}},actions:y};function D(w){return e.api.endpoints[w.endpointName].initiate(w.originalArgs,{subscribe:!1,forceRefetch:!0})}}var kt=Symbol(),er=({createSelector:e=Dt}={})=>({name:kt,init(t,{baseQuery:n,tagTypes:p,reducerPath:d,serializeQueryArgs:g,keepUnusedDataFor:y,refetchOnMountOrArgChange:E,refetchOnFocus:P,refetchOnReconnect:I,invalidationBehavior:D,onSchemaFailure:w,catchSchemaFailure:b,skipSchemaValidation:m},R){yr();const T=O=>O;Object.assign(t,{reducerPath:d,endpoints:{},internalActions:{onOnline:De,onOffline:it,onFocus:Le,onFocusLost:nt},util:{}});const A=ln({serializeQueryArgs:g,reducerPath:d,createSelector:e}),{selectInvalidatedBy:v,selectCachedArgsForQuery:M,buildQuerySelector:h,buildInfiniteQuerySelector:r,buildMutationSelector:a}=A;ne(t.util,{selectInvalidatedBy:v,selectCachedArgsForQuery:M});const{queryThunk:S,infiniteQueryThunk:x,mutationThunk:j,patchQueryData:f,updateQueryData:s,upsertQueryData:i,prefetch:o,buildMatchThunkActions:c}=cn({baseQuery:n,reducerPath:d,context:R,api:t,serializeQueryArgs:g,assertTagType:T,selectors:A,onSchemaFailure:w,catchSchemaFailure:b,skipSchemaValidation:m}),{reducer:u,actions:C}=un({context:R,queryThunk:S,mutationThunk:j,serializeQueryArgs:g,reducerPath:d,assertTagType:T,config:{refetchOnFocus:P,refetchOnReconnect:I,refetchOnMountOrArgChange:E,keepUnusedDataFor:y,reducerPath:d,invalidationBehavior:D}});ne(t.util,{patchQueryData:f,updateQueryData:s,upsertQueryData:i,prefetch:o,resetApiState:C.resetApiState,upsertQueryEntries:C.cacheEntriesUpserted}),ne(t.internalActions,C);const{middleware:k,actions:Q}=xn({reducerPath:d,context:R,queryThunk:S,mutationThunk:j,infiniteQueryThunk:x,api:t,assertTagType:T,selectors:A});ne(t.util,Q),ne(t,{reducer:u,middleware:k});const{buildInitiateQuery:L,buildInitiateInfiniteQuery:q,buildInitiateMutation:_,getRunningMutationThunk:H,getRunningMutationsThunk:U,getRunningQueriesThunk:K,getRunningQueryThunk:V}=on({queryThunk:S,mutationThunk:j,infiniteQueryThunk:x,api:t,serializeQueryArgs:g,context:R});return ne(t.util,{getRunningMutationThunk:H,getRunningMutationsThunk:U,getRunningQueryThunk:V,getRunningQueriesThunk:K}),{name:kt,injectEndpoint(O,$){var re;const W=(re=t.endpoints)[O]??(re[O]={});_e($)&&ne(W,{name:O,select:h(O,$),initiate:L(O,$)},c(S,O)),rn($)&&ne(W,{name:O,select:a(),initiate:_(O)},c(j,O)),Ne($)&&ne(W,{name:O,select:r(O,$),initiate:q(O,$)},c(S,O))}}}});er();const jn={user:null,token:localStorage.getItem("token"),refreshToken:localStorage.getItem("refreshToken"),isAuthenticated:!!localStorage.getItem("token"),isLoading:!1,error:null},tr=oe({name:"auth",initialState:jn,reducers:{loginStart:e=>{e.isLoading=!0,e.error=null},loginSuccess:(e,t)=>{e.isLoading=!1,e.isAuthenticated=!0,e.user=t.payload.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.error=null,localStorage.setItem("token",t.payload.token),localStorage.setItem("refreshToken",t.payload.refreshToken),localStorage.setItem("user",JSON.stringify(t.payload.user))},loginFailure:(e,t)=>{e.isLoading=!1,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null,e.error=t.payload,localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user")},logout:e=>{e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null,e.error=null,e.isLoading=!1,localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user")},clearError:e=>{e.error=null},updateUser:(e,t)=>{e.user=t.payload,localStorage.setItem("user",JSON.stringify(t.payload))},initializeAuth:e=>{const t=localStorage.getItem("token"),n=localStorage.getItem("refreshToken"),p=localStorage.getItem("user");if(t&&n&&p)try{const d=JSON.parse(p);e.token=t,e.refreshToken=n,e.user=d,e.isAuthenticated=!0}catch{e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null,localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user")}}}}),{loginStart:fi,loginSuccess:hi,loginFailure:pi,logout:In,clearError:mi,updateUser:yi,initializeAuth:An}=tr.actions,wn=tr.reducer,rr=e=>e.auth;function Pe(e){return e.replace(e[0],e[0].toUpperCase())}function Pn(e){return e.type==="query"}function Cn(e){return e.type==="mutation"}function nr(e){return e.type==="infinitequery"}function Se(e,...t){return Object.assign(e,...t)}var ze=Symbol();function $e(e,t,n,p){const d=N.useMemo(()=>({queryArgs:e,serialized:typeof e=="object"?t({queryArgs:e,endpointDefinition:n,endpointName:p}):e}),[e,t,n,p]),g=N.useRef(d);return N.useEffect(()=>{g.current.serialized!==d.serialized&&(g.current=d)},[d]),g.current.serialized===d.serialized?g.current.queryArgs:e}function Ce(e){const t=N.useRef(e);return N.useEffect(()=>{be(t.current,e)||(t.current=e)},[e]),be(t.current,e)?t.current:e}var En=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Rn=En(),kn=()=>typeof navigator<"u"&&navigator.product==="ReactNative",Tn=kn(),qn=()=>Rn||Tn?N.useLayoutEffect:N.useEffect,Mn=qn(),Tt=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:Bt.pending}:e;function Be(e,...t){const n={};return t.forEach(p=>{n[p]=e[p]}),n}var Ke=["data","status","isLoading","isSuccess","isError","error"];function Qn({api:e,moduleOptions:{batch:t,hooks:{useDispatch:n,useSelector:p,useStore:d},unstable__sideEffectsInRender:g,createSelector:y},serializeQueryArgs:E,context:P}){const I=g?r=>r():N.useEffect;return{buildQueryHooks:v,buildInfiniteQueryHooks:M,buildMutationHook:h,usePrefetch:b};function D(r,a,S){if(a!=null&&a.endpointName&&r.isUninitialized){const{endpointName:o}=a,c=P.endpointDefinitions[o];S!==X&&E({queryArgs:a.originalArgs,endpointDefinition:c,endpointName:o})===E({queryArgs:S,endpointDefinition:c,endpointName:o})&&(a=void 0)}let x=r.isSuccess?r.data:a==null?void 0:a.data;x===void 0&&(x=r.data);const j=x!==void 0,f=r.isLoading,s=(!a||a.isLoading||a.isUninitialized)&&!j&&f,i=r.isSuccess||j&&(f&&!(a!=null&&a.isError)||r.isUninitialized);return{...r,data:x,currentData:r.data,isFetching:f,isLoading:s,isSuccess:i}}function w(r,a,S){if(a!=null&&a.endpointName&&r.isUninitialized){const{endpointName:o}=a,c=P.endpointDefinitions[o];S!==X&&E({queryArgs:a.originalArgs,endpointDefinition:c,endpointName:o})===E({queryArgs:S,endpointDefinition:c,endpointName:o})&&(a=void 0)}let x=r.isSuccess?r.data:a==null?void 0:a.data;x===void 0&&(x=r.data);const j=x!==void 0,f=r.isLoading,s=(!a||a.isLoading||a.isUninitialized)&&!j&&f,i=r.isSuccess||f&&j;return{...r,data:x,currentData:r.data,isFetching:f,isLoading:s,isSuccess:i}}function b(r,a){const S=n(),x=Ce(a);return N.useCallback((j,f)=>S(e.util.prefetch(r,j,{...x,...f})),[r,S,x])}function m(r,a,{refetchOnReconnect:S,refetchOnFocus:x,refetchOnMountOrArgChange:j,skip:f=!1,pollingInterval:s=0,skipPollingIfUnfocused:i=!1,...o}={}){const{initiate:c}=e.endpoints[r],u=n(),C=N.useRef(void 0);if(!C.current){const O=u(e.internalActions.internal_getRTKQSubscriptions());C.current=O}const k=$e(f?X:a,Me,P.endpointDefinitions[r],r),Q=Ce({refetchOnReconnect:S,refetchOnFocus:x,pollingInterval:s,skipPollingIfUnfocused:i}),L=o.initialPageParam,q=Ce(L),_=N.useRef(void 0);let{queryCacheKey:H,requestId:U}=_.current||{},K=!1;H&&U&&(K=C.current.isRequestSubscribed(H,U));const V=!K&&_.current!==void 0;return I(()=>{V&&(_.current=void 0)},[V]),I(()=>{var F;const O=_.current;if(k===X){O==null||O.unsubscribe(),_.current=void 0;return}const $=(F=_.current)==null?void 0:F.subscriptionOptions;if(!O||O.arg!==k){O==null||O.unsubscribe();const W=u(c(k,{subscriptionOptions:Q,forceRefetch:j,...nr(P.endpointDefinitions[r])?{initialPageParam:q}:{}}));_.current=W}else Q!==$&&O.updateSubscriptionOptions(Q)},[u,c,j,k,Q,V,q,r]),[_,u,c,Q]}function R(r,a){return(x,{skip:j=!1,selectFromResult:f}={})=>{const{select:s}=e.endpoints[r],i=$e(j?X:x,E,P.endpointDefinitions[r],r),o=N.useRef(void 0),c=N.useMemo(()=>y([s(i),(L,q)=>q,L=>i],a,{memoizeOptions:{resultEqualityCheck:be}}),[s,i]),u=N.useMemo(()=>f?y([c],f,{devModeChecks:{identityFunctionCheck:"never"}}):c,[c,f]),C=p(L=>u(L,o.current),be),k=d(),Q=c(k.getState(),o.current);return Mn(()=>{o.current=Q},[Q]),C}}function T(r){N.useEffect(()=>()=>{var a,S;(S=(a=r.current)==null?void 0:a.unsubscribe)==null||S.call(a),r.current=void 0},[r])}function A(r){if(!r.current)throw new Error(Lt(38));return r.current.refetch()}function v(r){const a=(j,f={})=>{const[s]=m(r,j,f);return T(s),N.useMemo(()=>({refetch:()=>A(s)}),[s])},S=({refetchOnReconnect:j,refetchOnFocus:f,pollingInterval:s=0,skipPollingIfUnfocused:i=!1}={})=>{const{initiate:o}=e.endpoints[r],c=n(),[u,C]=N.useState(ze),k=N.useRef(void 0),Q=Ce({refetchOnReconnect:j,refetchOnFocus:f,pollingInterval:s,skipPollingIfUnfocused:i});I(()=>{var U,K;const H=(U=k.current)==null?void 0:U.subscriptionOptions;Q!==H&&((K=k.current)==null||K.updateSubscriptionOptions(Q))},[Q]);const L=N.useRef(Q);I(()=>{L.current=Q},[Q]);const q=N.useCallback(function(H,U=!1){let K;return t(()=>{var V;(V=k.current)==null||V.unsubscribe(),k.current=K=c(o(H,{subscriptionOptions:L.current,forceRefetch:!U})),C(H)}),K},[c,o]),_=N.useCallback(()=>{var H,U;(H=k.current)!=null&&H.queryCacheKey&&c(e.internalActions.removeQueryResult({queryCacheKey:(U=k.current)==null?void 0:U.queryCacheKey}))},[c]);return N.useEffect(()=>()=>{var H;(H=k==null?void 0:k.current)==null||H.unsubscribe()},[]),N.useEffect(()=>{u!==ze&&!k.current&&q(u,!0)},[u,q]),N.useMemo(()=>[q,u,{reset:_}],[q,u,_])},x=R(r,D);return{useQueryState:x,useQuerySubscription:a,useLazyQuerySubscription:S,useLazyQuery(j){const[f,s,{reset:i}]=S(j),o=x(s,{...j,skip:s===ze}),c=N.useMemo(()=>({lastArg:s}),[s]);return N.useMemo(()=>[f,{...o,reset:i},c],[f,o,i,c])},useQuery(j,f){const s=a(j,f),i=x(j,{selectFromResult:j===X||f!=null&&f.skip?void 0:Tt,...f}),o=Be(i,...Ke);return N.useDebugValue(o),N.useMemo(()=>({...i,...s}),[i,s])}}}function M(r){const a=(x,j={})=>{const[f,s,i,o]=m(r,x,j),c=N.useRef(o);I(()=>{c.current=o},[o]);const u=N.useCallback(function(Q,L){let q;return t(()=>{var _;(_=f.current)==null||_.unsubscribe(),f.current=q=s(i(Q,{subscriptionOptions:c.current,direction:L}))}),q},[f,s,i]);T(f);const C=$e(j.skip?X:x,Me,P.endpointDefinitions[r],r),k=N.useCallback(()=>A(f),[f]);return N.useMemo(()=>({trigger:u,refetch:k,fetchNextPage:()=>u(C,"forward"),fetchPreviousPage:()=>u(C,"backward")}),[k,u,C])},S=R(r,w);return{useInfiniteQueryState:S,useInfiniteQuerySubscription:a,useInfiniteQuery(x,j){const{refetch:f,fetchNextPage:s,fetchPreviousPage:i}=a(x,j),o=S(x,{selectFromResult:x===X||j!=null&&j.skip?void 0:Tt,...j}),c=Be(o,...Ke,"hasNextPage","hasPreviousPage");return N.useDebugValue(c),N.useMemo(()=>({...o,fetchNextPage:s,fetchPreviousPage:i,refetch:f}),[o,s,i,f])}}}function h(r){return({selectFromResult:a,fixedCacheKey:S}={})=>{const{select:x,initiate:j}=e.endpoints[r],f=n(),[s,i]=N.useState();N.useEffect(()=>()=>{s!=null&&s.arg.fixedCacheKey||s==null||s.reset()},[s]);const o=N.useCallback(function(H){const U=f(j(H,{fixedCacheKey:S}));return i(U),U},[f,j,S]),{requestId:c}=s||{},u=N.useMemo(()=>x({fixedCacheKey:S,requestId:s==null?void 0:s.requestId}),[S,s,x]),C=N.useMemo(()=>a?y([u],a):u,[a,u]),k=p(C,be),Q=S==null?s==null?void 0:s.arg.originalArgs:void 0,L=N.useCallback(()=>{t(()=>{s&&i(void 0),S&&f(e.internalActions.removeMutationResult({requestId:c,fixedCacheKey:S}))})},[f,S,s,c]),q=Be(k,...Ke,"endpointName");N.useDebugValue(q);const _=N.useMemo(()=>({...k,originalArgs:Q,reset:L}),[k,Q,L]);return N.useMemo(()=>[o,_],[o,_])}}}var On=Symbol(),Ln=({batch:e=jr,hooks:t={useDispatch:Vt,useSelector:Ft,useStore:Ir},createSelector:n=Dt,unstable__sideEffectsInRender:p=!1,...d}={})=>({name:On,init(g,{serializeQueryArgs:y},E){const P=g,{buildQueryHooks:I,buildInfiniteQueryHooks:D,buildMutationHook:w,usePrefetch:b}=Qn({api:g,moduleOptions:{batch:e,hooks:t,unstable__sideEffectsInRender:p,createSelector:n},serializeQueryArgs:y,context:E});return Se(P,{usePrefetch:b}),Se(E,{batch:e}),{injectEndpoint(m,R){if(Pn(R)){const{useQuery:T,useLazyQuery:A,useLazyQuerySubscription:v,useQueryState:M,useQuerySubscription:h}=I(m);Se(P.endpoints[m],{useQuery:T,useLazyQuery:A,useLazyQuerySubscription:v,useQueryState:M,useQuerySubscription:h}),g[`use${Pe(m)}Query`]=T,g[`useLazy${Pe(m)}Query`]=A}if(Cn(R)){const T=w(m);Se(P.endpoints[m],{useMutation:T}),g[`use${Pe(m)}Mutation`]=T}else if(nr(R)){const{useInfiniteQuery:T,useInfiniteQuerySubscription:A,useInfiniteQueryState:v}=D(m);Se(P.endpoints[m],{useInfiniteQuery:T,useInfiniteQuerySubscription:A,useInfiniteQueryState:v}),g[`use${Pe(m)}InfiniteQuery`]=T}}}}}),ir=Xt(er(),Ln());const Ee=ir({reducerPath:"authApi",baseQuery:Zt({baseUrl:"/api/auth",prepareHeaders:(e,{getState:t})=>{const n=t().auth.token;return n&&e.set("authorization",`Bearer ${n}`),e}}),tagTypes:["Auth"],endpoints:e=>({login:e.mutation({query:t=>({url:"login",method:"POST",body:t}),invalidatesTags:["Auth"]}),logout:e.mutation({query:()=>({url:"logout",method:"POST"}),invalidatesTags:["Auth"]}),getCurrentUser:e.query({query:()=>"me",providesTags:["Auth"]}),validateToken:e.query({query:()=>({url:"validate",method:"POST"}),providesTags:["Auth"]}),refreshToken:e.mutation({query:t=>({url:"refresh",method:"POST",body:t})}),getDemoUsers:e.query({query:()=>"demo-users"})})}),{useLoginMutation:gi,useLogoutMutation:Dn,useGetDemoUsersQuery:vi}=Ee,Ze=ir({reducerPath:"incidentApi",baseQuery:Zt({baseUrl:"/api/incident",prepareHeaders:(e,{getState:t})=>{const n=t().auth.token;return n&&e.set("authorization",`Bearer ${n}`),e.set("content-type","application/json"),e}}),tagTypes:["Incident","IncidentStatistics"],endpoints:e=>({createIncident:e.mutation({query:t=>({url:"",method:"POST",body:t}),invalidatesTags:["Incident","IncidentStatistics"]}),getIncidents:e.query({query:(t={})=>{const n=new URLSearchParams;return t.page&&n.append("page",t.page.toString()),t.pageSize&&n.append("pageSize",t.pageSize.toString()),t.status&&n.append("status",t.status),t.severity&&n.append("severity",t.severity),t.search&&n.append("search",t.search),{url:`?${n.toString()}`,method:"GET"}},providesTags:["Incident"]}),getIncident:e.query({query:t=>({url:`/${t}`,method:"GET"}),providesTags:(t,n,p)=>[{type:"Incident",id:p}]}),updateIncident:e.mutation({query:({id:t,incident:n})=>({url:`/${t}`,method:"PUT",body:n}),invalidatesTags:(t,n,{id:p})=>[{type:"Incident",id:p},"Incident","IncidentStatistics"]}),getMyIncidents:e.query({query:(t={})=>{const n=new URLSearchParams;return t.page&&n.append("page",t.page.toString()),t.pageSize&&n.append("pageSize",t.pageSize.toString()),t.status&&n.append("status",t.status),t.severity&&n.append("severity",t.severity),{url:`/my-reports?${n.toString()}`,method:"GET"}},providesTags:["Incident"]}),getIncidentStatistics:e.query({query:()=>({url:"/statistics",method:"GET"}),providesTags:["IncidentStatistics"]}),uploadIncidentAttachments:e.mutation({query:({incidentId:t,files:n})=>{const p=new FormData;return n.forEach((d,g)=>{p.append(`files[${g}]`,d)}),{url:`/${t}/attachments`,method:"POST",body:p,formData:!0}},invalidatesTags:(t,n,{incidentId:p})=>[{type:"Incident",id:p}]})})}),sr=Ar({reducer:{auth:wn,[Ee.reducerPath]:Ee.reducer,[Ze.reducerPath]:Ze.reducer},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}}).concat(Ee.middleware,Ze.middleware)});tn(sr.dispatch);var _n=["512 512","<polygon fill='var(--ci-primary-color, currentColor)' points='77.155 272.034 351.75 272.034 351.75 272.033 351.75 240.034 351.75 240.033 77.155 240.033 152.208 164.98 152.208 164.98 152.208 164.979 129.58 142.353 15.899 256.033 15.9 256.034 15.899 256.034 129.58 369.715 152.208 347.088 152.208 347.087 152.208 347.087 77.155 272.034' class='ci-primary'/><polygon fill='var(--ci-primary-color, currentColor)' points='160 16 160 48 464 48 464 464 160 464 160 496 496 496 496 16 160 16' class='ci-primary'/>"],Nn=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M450.27,348.569,406.6,267.945V184c0-83.813-68.187-152-152-152s-152,68.187-152,152v83.945L58.928,348.568A24,24,0,0,0,80.031,384h86.935c-.238,2.636-.367,5.3-.367,8a88,88,0,0,0,176,0c0-2.7-.129-5.364-.367-8h86.935a24,24,0,0,0,21.1-35.431ZM310.6,392a56,56,0,1,1-111.419-8H310.018A56.14,56.14,0,0,1,310.6,392ZM93.462,352,134.6,276.055V184a120,120,0,0,1,240,0v92.055L415.736,352Z' class='ci-primary'/>"],Hn=["512 512","<polygon fill='var(--ci-primary-color, currentColor)' points='376 160 376 192 441.372 192 252 381.373 180 309.373 76.686 412.686 99.314 435.314 180 354.627 252 426.627 464 214.628 464 280 496 280 496 160 376 160' class='ci-primary'/><polygon fill='var(--ci-primary-color, currentColor)' points='48 104 16 104 16 496 496 496 496 464 48 464 48 104' class='ci-primary'/>"],Fn=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M432,56H376V88h48V464H88V88h48V56H80A24.028,24.028,0,0,0,56,80V472a24.028,24.028,0,0,0,24,24H432a24.028,24.028,0,0,0,24-24V80A24.028,24.028,0,0,0,432,56Z' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M192,140H320a24.028,24.028,0,0,0,24-24V16H168V116A24.028,24.028,0,0,0,192,140Zm8-92H312v60H200Z' class='ci-primary'/>"],Vn=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M334.627,16H48V496H472V153.373ZM440,166.627V168H320V48h1.373ZM80,464V48H288V200H440V464Z' class='ci-primary'/>"],Un=["512 512","<rect width='352' height='32' x='80' y='96' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='352' height='32' x='80' y='240' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='352' height='32' x='80' y='384' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/>"],zn=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M245.151,168a88,88,0,1,0,88,88A88.1,88.1,0,0,0,245.151,168Zm0,144a56,56,0,1,1,56-56A56.063,56.063,0,0,1,245.151,312Z' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M464.7,322.319l-31.77-26.153a193.081,193.081,0,0,0,0-80.332l31.77-26.153a19.941,19.941,0,0,0,4.606-25.439l-32.612-56.483a19.936,19.936,0,0,0-24.337-8.73l-38.561,14.447a192.038,192.038,0,0,0-69.54-40.192L297.49,32.713A19.936,19.936,0,0,0,277.762,16H212.54a19.937,19.937,0,0,0-19.728,16.712L186.05,73.284a192.03,192.03,0,0,0-69.54,40.192L77.945,99.027a19.937,19.937,0,0,0-24.334,8.731L21,164.245a19.94,19.94,0,0,0,4.61,25.438l31.767,26.151a193.081,193.081,0,0,0,0,80.332l-31.77,26.153A19.942,19.942,0,0,0,21,347.758l32.612,56.483a19.937,19.937,0,0,0,24.337,8.73l38.562-14.447a192.03,192.03,0,0,0,69.54,40.192l6.762,40.571A19.937,19.937,0,0,0,212.54,496h65.222a19.936,19.936,0,0,0,19.728-16.712l6.763-40.572a192.038,192.038,0,0,0,69.54-40.192l38.564,14.449a19.938,19.938,0,0,0,24.334-8.731L469.3,347.755A19.939,19.939,0,0,0,464.7,322.319Zm-50.636,57.12-48.109-18.024-7.285,7.334a159.955,159.955,0,0,1-72.625,41.973l-10,2.636L267.6,464h-44.89l-8.442-50.642-10-2.636a159.955,159.955,0,0,1-72.625-41.973l-7.285-7.334L76.241,379.439,53.8,340.562l39.629-32.624-2.7-9.973a160.9,160.9,0,0,1,0-83.93l2.7-9.972L53.8,171.439l22.446-38.878,48.109,18.024,7.285-7.334a159.955,159.955,0,0,1,72.625-41.973l10-2.636L222.706,48H267.6l8.442,50.642,10,2.636a159.955,159.955,0,0,1,72.625,41.973l7.285,7.334,48.109-18.024,22.447,38.877-39.629,32.625,2.7,9.972a160.9,160.9,0,0,1,0,83.93l-2.7,9.973,39.629,32.623Z' class='ci-primary'/>"],$n=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M473.605,88.081c-1.352-.137-135.958-14.259-199.218-68.251L269.9,16H242.1l-4.488,3.83C174.464,73.727,39.744,87.944,38.4,88.081L24,89.532V104c0,89.133,14.643,165.443,43.523,226.813,38.105,80.973,100.1,133.669,184.267,156.623l4.21,1.148,4.21-1.148c84.165-22.954,146.162-75.65,184.267-156.623C473.357,269.443,488,193.133,488,104V89.532ZM455.87,118.113q-.237,12.789-.948,25.887H272V57.915C331.921,97.482,421.024,113.237,455.87,118.113ZM272,320H414.266A288.233,288.233,0,0,1,390.9,360H272Zm0-32V248H439.9a402.662,402.662,0,0,1-13.236,42.884V288Zm0-72V176H452.378c-1.4,13.307-3.256,26.682-5.639,40ZM56.13,118.113c34.846-4.876,123.949-20.631,183.87-60.2V450.224C94.012,398.389,58.492,245.387,56.13,118.113ZM272,450.224V392h92.347C340.049,416.7,309.708,436.836,272,450.224Z' class='ci-primary'/>"],Bn=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M425.706,142.294A240,240,0,0,0,16,312v88H160V368H48V312c0-114.691,93.309-208,208-208s208,93.309,208,208v56H352v32H496V312A238.432,238.432,0,0,0,425.706,142.294Z' class='ci-primary'/><rect width='32' height='32' x='80' y='264' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='32' x='240' y='128' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='32' x='136' y='168' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='32' x='400' y='264' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M297.222,335.1l69.2-144.173-28.85-13.848L268.389,321.214A64.141,64.141,0,1,0,297.222,335.1ZM256,416a32,32,0,1,1,32-32A32.036,32.036,0,0,1,256,416Z' class='ci-primary'/>"],Kn=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M222.085,235.644l-62.01-62.01L81.8,251.905l62.009,62.01-.04.04,66.958,66.957,11.354,11.275.04.039,66.957-66.957,11.273-11.354L502.628,111.644,424.356,33.373Zm44.33,66.958-11.274,11.353h0l-33.057,33.056-.04-.039-33.017-33.017.04-.04-62.009-62.01,33.016-33.016,62.01,62.009L424.356,78.627l33.017,33.017Z' class='ci-primary'/><polygon fill='var(--ci-primary-color, currentColor)' points='448 464 48 464 48 64 348.22 64 380.22 32 16 32 16 496 480 496 480 179.095 448 211.095 448 464' class='ci-primary'/>"],Zn=["512 512","<path fill='var(--ci-primary-color, currentColor)' d='M411.6,343.656l-72.823-47.334,27.455-50.334A80.23,80.23,0,0,0,376,207.681V128a112,112,0,0,0-224,0v79.681a80.236,80.236,0,0,0,9.768,38.308l27.455,50.333L116.4,343.656A79.725,79.725,0,0,0,80,410.732V496H448V410.732A79.727,79.727,0,0,0,411.6,343.656ZM416,464H112V410.732a47.836,47.836,0,0,1,21.841-40.246l97.66-63.479-41.64-76.341A48.146,48.146,0,0,1,184,207.681V128a80,80,0,0,1,160,0v79.681a48.146,48.146,0,0,1-5.861,22.985L296.5,307.007l97.662,63.479h0A47.836,47.836,0,0,1,416,410.732Z' class='ci-primary'/>"],Wn=["512 512","<rect width='32' height='176' x='240' y='176' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><rect width='32' height='32' x='240' y='384' fill='var(--ci-primary-color, currentColor)' class='ci-primary'/><path fill='var(--ci-primary-color, currentColor)' d='M274.014,16H237.986L16,445.174V496H496V445.174ZM464,464H48V452.959L256,50.826,464,452.959Z' class='ci-primary'/>"];const or=()=>Vt(),ar=Ft,Jn=()=>{const e=ar(rr),t=g=>{var y;return((y=e.user)==null?void 0:y.roles.includes(g))||!1},n=g=>{var y;return((y=e.user)==null?void 0:y.permissions.includes(g))||!1},p=g=>g.some(y=>t(y)),d=g=>g.some(y=>n(y));return{user:e.user,isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,error:e.error,hasRole:t,hasPermission:n,hasAnyRole:p,hasAnyPermission:d}},Gn=[{component:J,name:"Dashboard",to:"/dashboard",icon:l.jsx(G,{icon:Bn,customClassName:"nav-icon"})},{component:fe,name:"Incident Management"},{component:ke,name:"Incidents",to:"/incidents",icon:l.jsx(G,{icon:Wn,customClassName:"nav-icon"}),items:[{component:J,name:"Report Incident",to:"/incidents/create"},{component:J,name:"View Incidents",to:"/incidents"},{component:J,name:"My Reports",to:"/incidents/my-reports"}]},{component:fe,name:"Risk Management"},{component:ke,name:"Hazards",to:"/hazards",icon:l.jsx(G,{icon:Kn,customClassName:"nav-icon"}),items:[{component:J,name:"Report Hazard",to:"/hazards/report"},{component:J,name:"Hazard Register",to:"/hazards/register"}]},{component:J,name:"Risk Register",to:"/risks/register",icon:l.jsx(G,{icon:Fn,customClassName:"nav-icon"})},{component:fe,name:"Compliance"},{component:J,name:"Audits",to:"/audits",icon:l.jsx(G,{icon:Vn,customClassName:"nav-icon"})},{component:J,name:"Training",to:"/training",icon:l.jsx(G,{icon:$n,customClassName:"nav-icon"})},{component:fe,name:"Analytics"},{component:J,name:"Reports",to:"/reports",icon:l.jsx(G,{icon:Hn,customClassName:"nav-icon"})}],Yn=()=>{const e=or(),t=hr(),{user:n,isAuthenticated:p}=Jn(),[d]=Dn(),[g,y]=N.useState(!0),E=async()=>{try{await d().unwrap()}catch(P){console.warn("Logout API call failed:",P)}finally{e(In()),t("/login")}};return!p||!n?null:l.jsxs("div",{children:[l.jsxs(Pr,{position:"fixed",unfoldable:!1,visible:g,onVisibleChange:P=>y(P),className:"d-print-none sidebar sidebar-dark",children:[l.jsxs(Cr,{className:"d-none d-md-flex",href:"/",children:[l.jsx("div",{className:"sidebar-brand-full",children:l.jsx("strong",{children:"HarmoniHSE360"})}),l.jsx("div",{className:"sidebar-brand-minimized",children:l.jsx("strong",{children:"HSE"})})]}),l.jsx(Er,{children:Gn.map((P,I)=>{var D;return P.component===ke?l.jsx(ke,{toggler:l.jsxs(l.Fragment,{children:[P.icon,P.name]}),children:(D=P.items)==null?void 0:D.map((w,b)=>l.jsx(J,{children:l.jsx(ct,{to:w.to,className:"nav-link",children:w.name})},b))},I):P.component===fe?l.jsx(fe,{children:P.name},I):P.to?l.jsx(J,{children:l.jsxs(ct,{to:P.to,className:"nav-link",children:[P.icon,P.name]})},I):null})}),l.jsx(Rr,{className:"d-none d-lg-flex",onClick:()=>y(!g)})]}),l.jsxs("div",{className:"wrapper d-flex flex-column min-vh-100",children:[l.jsx(kr,{position:"sticky",className:"mb-4 p-0 ps-2",children:l.jsxs(ht,{fluid:!0,className:"px-4",children:[l.jsx(Tr,{onClick:()=>y(!g),style:{marginInlineStart:"-14px"},children:l.jsx(G,{icon:Un,size:"lg"})}),l.jsx(qr,{className:"mx-auto d-md-none",href:"/",children:"HarmoniHSE360"}),l.jsxs(Mr,{className:"ms-auto",children:[l.jsxs(pt,{variant:"nav-item",placement:"bottom-end",children:[l.jsxs(mt,{caret:!1,children:[l.jsx(G,{icon:Nn,size:"lg"}),l.jsx(Qr,{color:"danger",position:"top-end",shape:"rounded-pill",className:"p-1",children:"3"})]}),l.jsxs(yt,{className:"pt-0",children:[l.jsx(gt,{className:"bg-light fw-semibold py-2",children:"You have 3 notifications"}),l.jsx(le,{children:l.jsx("div",{className:"d-flex",children:l.jsxs("div",{className:"flex-grow-1",children:[l.jsx("h6",{className:"mb-1",children:"New incident reported"}),l.jsx("p",{className:"mb-1 small text-medium-emphasis",children:"Safety incident in Chemistry Lab"}),l.jsx("small",{className:"text-medium-emphasis",children:"2 min ago"})]})})}),l.jsx(Ve,{}),l.jsx(le,{href:"#",className:"text-center fw-semibold",children:"View all notifications"})]})]}),l.jsxs(pt,{variant:"nav-item",placement:"bottom-end",children:[l.jsx(mt,{className:"py-0",caret:!1,children:l.jsx(Or,{size:"md",color:"primary",textColor:"white",children:n.name.charAt(0).toUpperCase()})}),l.jsxs(yt,{className:"pt-0 pr-5 w-auto",children:[l.jsx(gt,{className:"bg-light fw-semibold py-2",children:n.name}),l.jsxs(le,{children:[l.jsx("div",{className:"small text-medium-emphasis",children:n.position}),l.jsx("div",{className:"small text-medium-emphasis",children:n.department})]}),l.jsx(Ve,{}),l.jsxs(le,{onClick:()=>t("/profile"),children:[l.jsx(G,{icon:Zn,className:"me-2"}),"Profile"]}),l.jsxs(le,{onClick:()=>t("/settings"),children:[l.jsx(G,{icon:zn,className:"me-2"}),"Settings"]}),l.jsx(Ve,{}),l.jsxs(le,{onClick:E,children:[l.jsx(G,{icon:_n,className:"me-2"}),"Logout"]})]})]})]})]})}),l.jsx("div",{className:"body flex-grow-1 px-4",children:l.jsx(ht,{lg:!0,children:l.jsx(Mt,{})})}),l.jsxs(Lr,{children:[l.jsxs("div",{children:[l.jsx("a",{href:"https://bsj.sch.id",target:"_blank",rel:"noopener noreferrer",children:"British School Jakarta"}),l.jsx("span",{className:"ms-1",children:"© 2025 HarmoniHSE360"})]}),l.jsxs("div",{className:"ms-auto",children:[l.jsx("span",{className:"me-1",children:"Powered by"}),l.jsx("a",{href:"https://coreui.io/react",target:"_blank",rel:"noopener noreferrer",children:"CoreUI React"})]})]})]})]})},Xn=()=>l.jsx("div",{className:"auth-layout",children:l.jsx(Mt,{})}),ei=({children:e,requiredRoles:t=[]})=>{const n=or(),p=ar(rr),d=Qt(),[g,y]=N.useState(!1);return N.useEffect(()=>{n(An()),y(!0)},[n]),g?p.isAuthenticated?t.length>0&&p.user&&!t.some(P=>{var I;return(I=p.user)==null?void 0:I.roles.includes(P)})?l.jsx("div",{className:"d-flex justify-content-center align-items-center min-vh-100",children:l.jsxs("div",{className:"text-center",children:[l.jsx("h3",{children:"Access Denied"}),l.jsx("p",{children:"You don't have permission to access this page."})]})}):l.jsx(l.Fragment,{children:e}):l.jsx(We,{to:"/login",state:{from:d},replace:!0}):l.jsx("div",{className:"d-flex justify-content-center align-items-center min-vh-100",children:l.jsx(Ut,{color:"primary"})})},ti=pe.lazy(()=>Oe(()=>import("./Dashboard-CtaojPyc.js"),__vite__mapDeps([0,1,2,3])).catch(e=>(console.error("Failed to load Dashboard:",e),{default:()=>l.jsx("div",{children:"Error loading Dashboard. Please refresh."})}))),ri=pe.lazy(()=>Oe(()=>import("./Login-D-vNQc28.js"),__vite__mapDeps([4,2,5,1,3])).catch(e=>(console.error("Failed to load Login:",e),{default:()=>l.jsx("div",{children:"Error loading Login. Please refresh."})}))),qt=pe.lazy(()=>Oe(()=>import("./IncidentList-BeYcGaGD.js"),__vite__mapDeps([6,2,1,3])).catch(e=>(console.error("Failed to load IncidentList:",e),{default:()=>l.jsx("div",{children:"Error loading Incident List. Please refresh."})}))),ni=pe.lazy(()=>Oe(()=>import("./CreateIncident-7ZWZLZ-w.js"),__vite__mapDeps([7,2,5,1,3])).catch(e=>(console.error("Failed to load CreateIncident:",e),{default:()=>l.jsx("div",{children:"Error loading Create Incident. Please refresh."})}))),ii=()=>l.jsx("div",{className:"d-flex justify-content-center align-items-center min-vh-100",children:l.jsx(Ut,{color:"primary"})}),si=()=>{const e=Qt();return N.useEffect(()=>{window.scrollTo(0,0),console.log("Route changed to:",e.pathname)},[e]),null};class oi extends pe.Component{constructor(t){super(t),this.state={hasError:!1}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,n){console.error("Error Boundary caught an error:",t,n)}render(){return this.state.hasError?l.jsx("div",{className:"d-flex justify-content-center align-items-center min-vh-100",children:l.jsxs("div",{className:"text-center",children:[l.jsx("h2",{children:"Something went wrong"}),l.jsx("p",{children:"Please refresh the page to continue."}),l.jsx("button",{className:"btn btn-primary",onClick:()=>window.location.reload(),children:"Refresh Page"})]})}):this.props.children}}function ai(){return N.useEffect(()=>{"serviceWorker"in navigator&&navigator.serviceWorker.register("/sw.js").catch(()=>{})},[]),l.jsx(oi,{children:l.jsx(wr,{store:sr,children:l.jsxs(pr,{children:[l.jsx(si,{}),l.jsx(N.Suspense,{fallback:l.jsx(ii,{}),children:l.jsxs(mr,{children:[l.jsx(Y,{element:l.jsx(Xn,{}),children:l.jsx(Y,{path:"/login",element:l.jsx(ri,{})})}),l.jsxs(Y,{element:l.jsx(ei,{children:l.jsx(Yn,{})}),children:[l.jsx(Y,{path:"/",element:l.jsx(We,{to:"/dashboard",replace:!0})}),l.jsx(Y,{path:"/dashboard",element:l.jsx(ti,{})}),l.jsx(Y,{path:"/incidents",element:l.jsx(qt,{})}),l.jsx(Y,{path:"/incidents/create",element:l.jsx(ni,{})}),l.jsx(Y,{path:"/incidents/my-reports",element:l.jsx(qt,{})}),l.jsx(Y,{path:"/profile",element:l.jsxs("div",{className:"p-4",children:[l.jsx("h2",{children:"Profile Page"}),l.jsx("p",{children:"Coming soon..."})]})}),l.jsx(Y,{path:"/settings",element:l.jsxs("div",{className:"p-4",children:[l.jsx("h2",{children:"Settings Page"}),l.jsx("p",{children:"Coming soon..."})]})}),l.jsx(Y,{path:"*",element:l.jsx(We,{to:"/dashboard",replace:!0})})]})]})})]})})})}Je.createRoot(document.getElementById("root")).render(l.jsx(pe.StrictMode,{children:l.jsx(ai,{})}));export{Wn as a,Kn as b,$n as c,Fn as d,or as e,ar as f,gi as g,vi as h,mi as i,l as j,Zn as k,fi as l,hi as m,pi as n,Vn as o,Bn as p,rr as s,Jn as u};
